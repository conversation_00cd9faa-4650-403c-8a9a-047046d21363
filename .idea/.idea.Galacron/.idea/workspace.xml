<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="c8fdb8d1-42cc-46f5-9226-20ec97cbab00" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/.idea/.idea.Galacron/.idea/AugmentWebviewStateStore.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/.idea.Galacron/.idea/AugmentWebviewStateStore.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/.idea.Galacron/.idea/vcs.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/.idea.Galacron/.idea/vcs.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/.idea.Galacron/.idea/workspace.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/.idea.Galacron/.idea/workspace.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Galacron/Fonts/pixelart/pixelart SDF.asset" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Galacron/Fonts/pixelart/pixelart SDF.asset" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Galacron/Fonts/retro gaming/retro gaming SDF.asset" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Galacron/Fonts/retro gaming/retro gaming SDF.asset" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Galacron/Fonts/tight pixel/tight pixel SDF.asset" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Galacron/Fonts/tight pixel/tight pixel SDF.asset" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Galacron/Scenes/Game.unity" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Galacron/Scenes/Game.unity" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Packages/manifest.json" beforeDir="false" afterPath="$PROJECT_DIR$/Packages/manifest.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Packages/packages-lock.json" beforeDir="false" afterPath="$PROJECT_DIR$/Packages/packages-lock.json" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="DpaMonitoringSettings">
    <option name="autoShow" value="false" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/Assets/Packages/Ludo-AudioFlux" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$PROJECT_DIR$/Assets/Galacron/Scripts/Game/Management/Level/States/LevelBaseState.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Galacron/Scripts/Game/Splines/ISplineGenerator.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Galacron/Scripts/Installers/CoreManagersInstaller.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Galacron/Scripts/UI/MainMenu/MainButtonController.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Galacron/Scripts/UI/MainMenu/MainMenuController.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Galacron/Scripts/UI/MainMenu/SettingsPanelController.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Ludo/Core/Vibrations/Runtime/AndroidVibrationHandler.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Ludo/Core/Vibrations/Runtime/Plugins/iOS/Vibration.h" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Ludo/MonoHooks/Runtime/Events/CollisionEvent.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Packages/Ludo-Analytics/Runtime/IAnalyticsService.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Packages/Ludo-AudioFlux/Editor/SFXMenuItems.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Packages/Ludo-AudioFlux/Editor/SFXServiceEditor.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Packages/Ludo-AudioFlux/Editor/SoundDefinitionEditor.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Packages/Ludo-AudioFlux/Runtime/ISFXService.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Packages/Ludo-AudioFlux/Runtime/Modules/Advanced3D/AudioOcclusionModule.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Packages/Ludo-AudioFlux/Runtime/SFXService.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Packages/Ludo-AudioFlux/Runtime/SoundDefinition.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Packages/Ludo-AudioFlux/Runtime/SoundEvents.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Packages/Ludo-AudioFlux/Runtime/SoundHandle.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Packages/Ludo-AudioFlux/Runtime/SoundPlayParams.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Library/PackageCache/com.liteninja.unityinject/Runtime/ScriptableObjectInstaller.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Library/PackageCache/com.unity.burst/Runtime/BurstCompilerOptions.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Library/PackageCache/com.unity.ide.visualstudio/Editor/UnityInstallation.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Library/PackageCache/com.unity.inputsystem/Samples~/InGameHints/InGameHintsActions.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Library/PackageCache/com.unity.splines/Runtime/SplineAnimate.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Library/PackageCache/com.unity.timeline/Editor/TimelineUtility.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Library/PackageCache/com.unity.ugui/Editor/TMP/TMP_BitmapShaderGUI.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Library/PackageCache/com.unity.ugui/Editor/TMP/TMP_UIStyleManager.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Library/PackageCache/com.unity.ugui/Runtime/TMP/TMP_Dropdown.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Library/PackageCache/com.unity.ugui/Tests/Runtime/UGUI/Graphic/ImageTests.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Library/PackageCache/com.unity.ugui/Tests/Runtime/UGUI/TextEditor/TextEditorTests.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Library/PackageCache/com.unity.visualscripting/Runtime/VisualScripting.Core/Dependencies/FullSerializer/fsObjectProcessor.cs" root0="SKIP_HIGHLIGHTING" />
  </component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="MetaFilesCheckinStateConfiguration" checkMetaFiles="true" />
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 1
}</component>
  <component name="ProjectId" id="2rtczSwICQ5Aqdosl6tfaNEkCqh" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="1" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Attach to Unity Editor.Attach to Unity Editor.executor&quot;: &quot;Debug&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;main&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RunManager" selected="Attach to Unity Editor.Attach to Unity Editor">
    <configuration name="Start Unity" type="RunUnityExe" factoryName="Unity Executable">
      <option name="EXE_PATH" value="/Applications/Unity/Hub/Editor/6000.0.29f1/Unity.app/Contents/MacOS/Unity" />
      <option name="PROGRAM_PARAMETERS" value="-projectPath $PROJECT_DIR$ -debugCodeOptimization" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="PASS_PARENT_ENVS" value="1" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="ENV_FILE_PATHS" value="" />
      <option name="REDIRECT_INPUT_PATH" value="" />
      <option name="PTY_MODE" value="Auto" />
      <method v="2" />
    </configuration>
    <configuration name="Unit Tests (batch mode)" type="RunUnityExe" factoryName="Unity Executable">
      <option name="EXE_PATH" value="/Applications/Unity/Hub/Editor/6000.0.29f1/Unity.app/Contents/MacOS/Unity" />
      <option name="PROGRAM_PARAMETERS" value="-runTests -batchmode -projectPath $PROJECT_DIR$ -testResults Logs/results.xml -logFile Logs/Editor.log -testPlatform EditMode -debugCodeOptimization" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="PASS_PARENT_ENVS" value="1" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="ENV_FILE_PATHS" value="" />
      <option name="REDIRECT_INPUT_PATH" value="" />
      <option name="PTY_MODE" value="Auto" />
      <method v="2" />
    </configuration>
    <configuration name="Attach to Unity Editor &amp; Play" type="UNITY_DEBUG_RUN_CONFIGURATION" factoryName="UNITY_ATTACH_AND_PLAY" show_console_on_std_err="false" show_console_on_std_out="false" port="50000" address="localhost">
      <option name="allowRunningInParallel" value="false" />
      <option name="listenPortForConnections" value="false" />
      <option name="pid" />
      <option name="projectPathOnTarget" />
      <option name="runtimes">
        <list />
      </option>
      <option name="selectedOptions">
        <list />
      </option>
      <option name="useMixedMode" value="false" />
      <method v="2" />
    </configuration>
    <configuration name="Attach to Unity Editor" type="UNITY_DEBUG_RUN_CONFIGURATION" factoryName="Unity Debug" show_console_on_std_err="false" show_console_on_std_out="false" port="50000" address="localhost">
      <option name="allowRunningInParallel" value="false" />
      <option name="listenPortForConnections" value="false" />
      <option name="pid" />
      <option name="projectPathOnTarget" />
      <option name="runtimes">
        <list />
      </option>
      <option name="selectedOptions">
        <list />
      </option>
      <option name="useMixedMode" value="false" />
      <method v="2" />
    </configuration>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="c8fdb8d1-42cc-46f5-9226-20ec97cbab00" name="Changes" comment="" />
      <created>1737382074969</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1737382074969</updated>
      <workItem from="1737382078423" duration="111974000" />
      <workItem from="1740089524209" duration="58547000" />
      <workItem from="1740432692889" duration="13384000" />
      <workItem from="1740563307530" duration="25986000" />
      <workItem from="1740762102436" duration="44161000" />
      <workItem from="1742314611393" duration="2083000" />
      <workItem from="1743355305496" duration="64636000" />
      <workItem from="1744214058186" duration="20415000" />
      <workItem from="1744366870196" duration="68569000" />
      <workItem from="1745674872296" duration="16034000" />
      <workItem from="1746602691486" duration="29655000" />
      <workItem from="1748339131791" duration="6726000" />
      <workItem from="1748348345623" duration="12771000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="UnityCheckinConfiguration" checkUnsavedScenes="true" />
  <component name="UnityProjectConfiguration" hasMinimizedUI="true" />
  <component name="UnityProjectDiscoverer">
    <option name="hasUnityReference" value="true" />
    <option name="unityProject" value="true" />
    <option name="unityProjectFolder" value="true" />
  </component>
  <component name="UnityUnitTestConfiguration" currentTestLauncher="Both" />
  <component name="VcsManagerConfiguration">
    <option name="CLEAR_INITIAL_COMMIT_MESSAGE" value="true" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.OperationCanceledException" breakIfHandledByOtherCode="false" displayValue="System.OperationCanceledException" />
          <option name="timeStamp" value="1" />
        </breakpoint>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.Threading.Tasks.TaskCanceledException" breakIfHandledByOtherCode="false" displayValue="System.Threading.Tasks.TaskCanceledException" />
          <option name="timeStamp" value="2" />
        </breakpoint>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.Threading.ThreadAbortException" breakIfHandledByOtherCode="false" displayValue="System.Threading.ThreadAbortException" />
          <option name="timeStamp" value="3" />
        </breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>