<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:editor="UnityEditor.UIElements" xmlns:engine="UnityEngine.UIElements" xmlns="UnityEditor.Accessibility" elementFormDefault="qualified" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:import schemaLocation="UnityEngine.UIElements.xsd" namespace="UnityEngine.UIElements" />
  <xs:include schemaLocation="GlobalNamespace.xsd" />
  <xs:import schemaLocation="UnityEditor.ShaderGraph.Drawing.xsd" namespace="UnityEditor.ShaderGraph.Drawing" />
  <xs:import schemaLocation="UnityEditor.Rendering.xsd" namespace="UnityEditor.Rendering" />
  <xs:import schemaLocation="UnityEditor.Tilemaps.xsd" namespace="UnityEditor.Tilemaps" />
  <xs:import schemaLocation="UnityEditor.Tilemaps.External.xsd" namespace="UnityEditor.Tilemaps.External" />
  <xs:import schemaLocation="UnityEditor.U2D.Sprites.SpriteEditorTool.xsd" namespace="UnityEditor.U2D.Sprites.SpriteEditorTool" />
  <xs:import schemaLocation="UnityEditor.U2D.Animation.xsd" namespace="UnityEditor.U2D.Animation" />
  <xs:import schemaLocation="UnityEditor.U2D.Layout.xsd" namespace="UnityEditor.U2D.Layout" />
  <xs:import schemaLocation="UnityEditor.U2D.Animation.SpriteLibraryEditor.xsd" namespace="UnityEditor.U2D.Animation.SpriteLibraryEditor" />
  <xs:import schemaLocation="UnityEditor.U2D.Animation.Upgrading.xsd" namespace="UnityEditor.U2D.Animation.Upgrading" />
  <xs:import schemaLocation="UnityEditor.UIElements.Debugger.xsd" namespace="UnityEditor.UIElements.Debugger" />
  <xs:import schemaLocation="Unity.UI.Builder.xsd" namespace="Unity.UI.Builder" />
  <xs:import schemaLocation="UnityEditor.Search.xsd" namespace="UnityEditor.Search" />
  <xs:import schemaLocation="UnityEditor.Experimental.GraphView.xsd" namespace="UnityEditor.Experimental.GraphView" />
  <xs:import schemaLocation="UnityEditor.PackageManager.UI.Internal.xsd" namespace="UnityEditor.PackageManager.UI.Internal" />
  <xs:import schemaLocation="UnityEditor.ShortcutManagement.xsd" namespace="UnityEditor.ShortcutManagement" />
  <xs:import schemaLocation="UnityEditor.UIElements.xsd" namespace="UnityEditor.UIElements" />
  <xs:import schemaLocation="UnityEditor.Audio.UIElements.xsd" namespace="UnityEditor.Audio.UIElements" />
  <xs:import schemaLocation="Unity.Profiling.Editor.UI.xsd" namespace="Unity.Profiling.Editor.UI" />
  <xs:import schemaLocation="UnityEditor.UIElements.ProjectSettings.xsd" namespace="UnityEditor.UIElements.ProjectSettings" />
  <xs:import schemaLocation="UnityEditor.Overlays.xsd" namespace="UnityEditor.Overlays" />
  <xs:import schemaLocation="Unity.Profiling.Editor.xsd" namespace="Unity.Profiling.Editor" />
  <xs:import schemaLocation="UnityEditor.Inspector.GraphicsSettingsInspectors.xsd" namespace="UnityEditor.Inspector.GraphicsSettingsInspectors" />
  <xs:import schemaLocation="UnityEditor.Inspector.xsd" namespace="UnityEditor.Inspector" />
  <xs:import schemaLocation="UnityEditor.Accessibility.xsd" namespace="UnityEditor.Accessibility" />
</xs:schema>