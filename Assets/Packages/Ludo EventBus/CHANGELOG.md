# Changelog

All notable changes to the Ludo Core EventBus package will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).


## [1.1.0] - 2025-05-09
### Added
- Initial release of the EventBus system
- Type-safe event publishing and subscribing
- Priority-based event handling
- Memory safety with weak references
- Automatic cleanup for MonoBehaviour subscribers
- Event cancellation support
- Comprehensive diagnostics system
- Documentation and examples

## [1.0.0] - 2025-05-09
First release