# USpring Documentation

Welcome to the USpring documentation! This folder contains comprehensive documentation for the USpring system, a physically-based spring animation library for Unity.

## Documentation Structure

| Document | Description |
|----------|-------------|
| [**Architecture Overview**](USpring-Architecture-Overview.md) | High-level overview of the USpring architecture and design principles |
| [**API Documentation**](USpring-API-Documentation.md) | Detailed reference for interfaces, classes, and methods |
| [**Usage Guide**](USpring-Usage-Guide.md) | Step-by-step guide with practical examples |
| [**Patterns & Anti-Patterns**](USpring-Patterns-AntiPatterns.md) | Best practices and common pitfalls to avoid |

## Recommended Reading Order

1. **For beginners**: Start with the [main README](../README.md) for a quick overview, then read the [Usage Guide](USpring-Usage-Guide.md)
2. **For intermediate users**: Read the [Architecture Overview](USpring-Architecture-Overview.md) to understand the system design
3. **For advanced users**: Explore the [API Documentation](USpring-API-Documentation.md) and [Patterns & Anti-Patterns](USpring-Patterns-AntiPatterns.md)

## Common Use Cases

USpring is ideal for a wide range of animation needs:

- **UI Animation**: Create responsive, natural-feeling UI elements
- **Camera Effects**: Implement smooth camera movements and shake effects
- **Character Animation**: Add secondary motion to characters and objects
- **Visual Feedback**: Provide organic visual feedback for user interactions
- **Procedural Animation**: Generate dynamic, physically-based animations at runtime

## Additional Resources

- The [Physics README](../Runtime/Physics/README.md) provides details on the physics implementation
- The XML documentation in the code provides detailed information about specific methods and classes
- Demo scenes in the `Assets/Demos/USpring/Scenes` folder provide practical examples

---

© Ludo Games. All rights reserved.
