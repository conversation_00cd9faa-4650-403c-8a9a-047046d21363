{"name": "com.liteninja.uspring", "version": "1.1.0", "displayName": "<PERSON><PERSON>", "description": "A physically-based spring animation system for Unity that simulates damped harmonic oscillation, providing natural and responsive animations.", "unity": "2021.3", "documentationUrl": "https://github.com/sponticelli/Ludo-USpring/blob/main/Documentation/README.md", "changelogUrl": "https://github.com/sponticelli/Ludo-USpring/blob/main/CHANGELOG.md", "licensesUrl": "https://github.com/sponticelli/Ludo-USpring/blob/main/LICENSE", "dependencies": {}, "keywords": ["ludo", "spring", "animation", "physics", "interpolation", "damping", "oscillation"], "author": {"name": "<PERSON><PERSON>", "url": "https://github.com/sponticelli"}}