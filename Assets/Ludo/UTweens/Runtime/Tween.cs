using System;
using Ludo.UTweens.Plugins;
using UnityEngine;

namespace Ludo.UTweens
{
    /// <summary>
    /// Tween implementation for all value types
    /// </summary>
    public abstract class Tween : ITween
    {
        protected TweenData _data;
        
        protected bool _started;
        protected bool _isComplete;
        protected bool _killed;
        
        public TweenData TweenData => _data;
        
        public bool Active => _data.Active && !_killed;
        public bool Paused => _data.Paused;
        public bool IsPlaying => Active && !Paused;
        public bool IsComplete => _isComplete;
        public bool IsBackwards => _data.IsBackwards;
        public bool Recyclable { get => _data.Recyclable; set => _data.Recyclable = value; }
        public float Delay => _data.Delay;
        public float Duration => _data.Duration;
        public float ElapsedPercentage => _data.Duration > 0 ? Mathf.Clamp01(_data.ElapsedTime / _data.Duration) : 1f;
        public float ElapsedTime => _data.ElapsedTime;
        public int Loops => _data.Loops;
        public LoopType LoopType => _data.LoopType;
        public object Target => _data.Target;
        public object Id => _data.Id;
        public bool TimeScaleIndependent { get => _data.TimeScaleIndependent; set => _data.TimeScaleIndependent = value; }
        public UpdateType UpdateType 
        { 
            get => _data.UpdateType; 
            set 
            { 
                if (_data.UpdateType != value)
                {
                    UpdateType oldType = _data.UpdateType;
                    _data.UpdateType = value;
                    // Notify UTween to update registration
                    UTween.OnTweenUpdateTypeChanged(this, oldType, value);
                }
            } 
        }
        
        public event Action<ITween> OnStartEvent
        {
            add { _data.OnStart += value; }
            remove { _data.OnStart -= value; }
        }
        
        public event Action<ITween> OnPlayEvent
        {
            add { _data.OnPlay += value; }
            remove { _data.OnPlay -= value; }
        }
        
        public event Action<ITween> OnPauseEvent
        {
            add { _data.OnPause += value; }
            remove { _data.OnPause -= value; }
        }
        
        public event Action<ITween> OnUpdateEvent
        {
            add { _data.OnUpdate += value; }
            remove { _data.OnUpdate -= value; }
        }
        
        public event Action<ITween> OnStepCompleteEvent
        {
            add { _data.OnStepComplete += value; }
            remove { _data.OnStepComplete -= value; }
        }
        
        public event Action<ITween> OnCompleteEvent
        {
            add { _data.OnComplete += value; }
            remove { _data.OnComplete -= value; }
        }
        
        public event Action<ITween> OnKillEvent
        {
            add { _data.OnKill += value; }
            remove { _data.OnKill -= value; }
        }
        
        public event Action<ITween> OnRewindEvent
        {
            add { _data.OnRewind += value; }
            remove { _data.OnRewind -= value; }
        }
        
        public event Action<ITween, object, object> OnIdChangedEvent
        {
            add { _data.OnIdChanged += value; }
            remove { _data.OnIdChanged -= value; }
        }
        
        protected Tween(TweenData data)
        {
            _data = data;
        }
        
        public void Play()
        {
            if (_killed) return;
            
            if (_data.Paused)
            {
                _data.Paused = false;
                _data.OnPlay?.Invoke(this);
            }
        }
        
        public void Pause()
        {
            if (_killed || _data.Paused) return;
            
            _data.Paused = true;
            _data.OnPause?.Invoke(this);
        }
        
        public void TogglePause()
        {
            if (_killed) return;
            
            if (_data.Paused)
                Play();
            else
                Pause();
        }
        
        public void Rewind(bool includeDelay = true)
        {
            if (_killed) return;
            
            _data.ElapsedTime = includeDelay ? 0 : _data.Delay;
            _data.CompletedLoops = 0;
            _isComplete = false;
            _started = false;
            
            UpdateValue();
            
            _data.OnRewind?.Invoke(this);
        }
        
        public void Restart(bool includeDelay = true)
        {
            if (_killed) return;
            
            Rewind(includeDelay);
            Play();
        }
        
        public void Complete(bool withCallbacks = false)
        {
            if (_killed || _isComplete) return;
            
            // Set to end of tween
            if (_data.Loops <= 0)
            {
                _data.ElapsedTime = _data.Duration;
                _data.CompletedLoops = 1;
            }
            else
            {
                _data.ElapsedTime = _data.Duration;
                _data.CompletedLoops = _data.Loops;
            }
            
            UpdateValue();
            
            if (withCallbacks)
            {
                _data.OnStepComplete?.Invoke(this);
                _data.OnComplete?.Invoke(this);
            }
            
            _isComplete = true;
            
            if (_data.AutoKill)
                Kill(false);
        }
        
        public void Kill(bool complete = false)
        {
            if (_killed) return;
            
            if (complete && !_isComplete)
                Complete(true);
            
            _killed = true;
            
            _data.OnKill?.Invoke(this);
            
            // Unregister this tween
            UTween.UnregisterTween(this);
        }
        
        public void Flip()
        {
            if (_killed) return;
            
            _data.IsBackwards = !_data.IsBackwards;
        }
        
        public void Goto(float position, bool andPlay = false)
        {
            if (_killed) return;
            
            if (position < 0) position = 0;
            
            _data.ElapsedTime = position;
            UpdateValue();
            
            if (andPlay)
                Play();
        }
        
        public void Update(float deltaTime)
        {
            if (_killed || _data.Paused || _isComplete) return;
            
            // Check if we need to apply delay
            if (_data.ElapsedTime < _data.Delay)
            {
                _data.ElapsedTime += deltaTime;
                if (_data.ElapsedTime < _data.Delay) return;
            }
            
            // Start callback
            if (!_started)
            {
                _started = true;
                _data.OnStart?.Invoke(this);
            }
            
            // Apply deltaTime
            _data.ElapsedTime += _data.IsBackwards ? -deltaTime : deltaTime;
            
            // Clamp and check for loop completion
            bool loopComplete = false;
            if (_data.IsBackwards && _data.ElapsedTime <= _data.Delay)
            {
                _data.ElapsedTime = _data.Delay;
                loopComplete = true;
            }
            else if (!_data.IsBackwards && _data.ElapsedTime >= _data.Duration + _data.Delay)
            {
                _data.ElapsedTime = _data.Duration + _data.Delay;
                loopComplete = true;
            }
            
            // Update value
            UpdateValue();
            
            // Handle update callback
            _data.OnUpdate?.Invoke(this);
            
            // Handle loop completion
            if (loopComplete)
            {
                _data.OnStepComplete?.Invoke(this);
                
                // Handle looping
                if (_data.Loops == 0 || (_data.Loops > 0 && ++_data.CompletedLoops >= _data.Loops))
                {
                    // All loops completed
                    _isComplete = true;
                    _data.OnComplete?.Invoke(this);
                    
                    if (_data.AutoKill)
                        Kill(false);
                    
                    return;
                }

                if (_data.Loops > 0 || _data.Loops <= -1) // -1 means infinite loops
                {
                    // More loops to go
                    switch (_data.LoopType)
                    {
                        case LoopType.Restart:
                            _data.ElapsedTime = _data.IsBackwards ? _data.Duration + _data.Delay : _data.Delay;
                            break;
                            
                        case LoopType.Yoyo:
                            _data.IsBackwards = !_data.IsBackwards;
                            break;
                            
                        case LoopType.Incremental:
                            // The value doesn't reset, but time does
                            _data.ElapsedTime = _data.IsBackwards ? _data.Duration + _data.Delay : _data.Delay;
                            break;
                    }
                }
            }
        }
        
        protected abstract void UpdateValue();
        
        public ITween SetId(object id)
        {
            // Trigger change callback if id is different
            if (_data.Id != id)
            {
                object oldId = _data.Id;
                _data.Id = id;
                _data.OnIdChanged?.Invoke(this, oldId, id);
            }
            
            return this;
        }
        
        public ITween SetTarget(object target)
        {
            _data.Target = target;
            return this;
        }
        
        public ITween SetDelay(float delay)
        {
            _data.Delay = delay;
            return this;
        }
        
        public ITween SetLoops(int loops, LoopType loopType = LoopType.Restart)
        {
            _data.Loops = loops;
            _data.LoopType = loopType;
            return this;
        }
        
        public ITween SetAutoKill(bool autoKill)
        {
            _data.AutoKill = autoKill;
            return this;
        }
        
        public ITween SetRecyclable(bool recyclable)
        {
            _data.Recyclable = recyclable;
            return this;
        }
        
        public ITween SetEase(Ease ease)
        {
            _data.EaseType = ease;
            _data.UseCustomCurve = ease == Ease.CustomCurve;
            return this;
        }
        
        public ITween SetEase(AnimationCurve curve)
        {
            _data.EaseCurve = curve;
            _data.UseCustomCurve = true;
            return this;
        }
        
        public ITween SetTimeScaleIndependent(bool timeScaleIndependent)
        {
            _data.TimeScaleIndependent = timeScaleIndependent;
            return this;
        }
        
        public ITween SetUpdateType(UpdateType updateType)
        {
            UpdateType = updateType;
            return this;
        }
        
        public ITween OnStart(Action<ITween> callback)
        {
            _data.OnStart += callback;
            return this;
        }
        
        public ITween OnPlay(Action<ITween> callback)
        {
            _data.OnPlay += callback;
            return this;
        }
        
        public ITween OnPause(Action<ITween> callback)
        {
            _data.OnPause += callback;
            return this;
        }
        
        public ITween OnUpdate(Action<ITween> callback)
        {
            _data.OnUpdate += callback;
            return this;
        }
        
        public ITween OnStepComplete(Action<ITween> callback)
        {
            _data.OnStepComplete += callback;
            return this;
        }
        
        public ITween OnComplete(Action<ITween> callback)
        {
            _data.OnComplete += callback;
            return this;
        }
        
        public ITween OnKill(Action<ITween> callback)
        {
            _data.OnKill += callback;
            return this;
        }
        
        public ITween OnRewind(Action<ITween> callback)
        {
            _data.OnRewind += callback;
            return this;
        }

        public void GotoWithCallbacks(float position, bool andPlay = false)
        {
            if (_killed) return;
    
            position = Mathf.Clamp(position, _data.Delay, _data.Duration + _data.Delay);
            float originalPosition = _data.ElapsedTime;
            bool wasMovingForward = !_data.IsBackwards;
            bool triggeredStepComplete = false;
    
            // If we're moving backwards and moving past the start, trigger OnComplete if we haven't yet
            if (originalPosition > position && originalPosition >= _data.Duration + _data.Delay && !_isComplete)
            {
                _data.OnComplete?.Invoke(this);
            }
    
            // If we're moving forward and moving past the end, trigger OnStepComplete
            if (originalPosition < position && position >= _data.Duration + _data.Delay && !_isComplete && !triggeredStepComplete)
            {
                _data.OnStepComplete?.Invoke(this);
                triggeredStepComplete = true;
            }
    
            // Reset completion flags if we're moving back from the end
            if (originalPosition >= _data.Duration + _data.Delay && position < _data.Duration + _data.Delay)
            {
                _isComplete = false;
            }
    
            // Process actual position update
            Goto(position, false);
    
            // If we're at the end and haven't triggered OnComplete, do it now
            if (position >= _data.Duration + _data.Delay && !_isComplete && !triggeredStepComplete)
            {
                _data.OnStepComplete?.Invoke(this);
                if (_data.Loops <= 0 || (_data.Loops > 0 && _data.CompletedLoops >= _data.Loops))
                {
                    _isComplete = true;
                    _data.OnComplete?.Invoke(this);
                }
            }
    
            if (andPlay)
                Play();
        }
    }
    
    /// <summary>
    /// Generic tween implementation for different value types
    /// </summary>
    public class Tween<T> : Tween
    {
        protected Func<T> _getter;
        protected Action<T> _setter;
        protected T _startValue;
        protected T _endValue;
        protected bool _relative;
        protected bool _initialized;
        protected bool _fromValue;

        // Options
        protected bool _snapping;
        
        private ITweenPlugin<T> _plugin;
        
        public bool Snapping => _snapping;

        public Tween(TweenData data, Func<T> getter, Action<T> setter, T endValue) : base(data)
        {
            _getter = getter;
            _setter = setter;
            _endValue = endValue;
            _relative = false;
            _initialized = false;
            _fromValue = false;

            // Default options
            _snapping = false;
            
            // Get the appropriate plugin
            _plugin = PluginManager.GetPlugin<T>();
        }
        
        protected override void UpdateValue()
        {
            if (_killed || _setter == null || (_getter != null && _getter.Target == null)) return;

            // Initialize start value if needed
            if (!_initialized)
            {
                _startValue = _getter != null ? _getter() : default;
                _initialized = true;
        
                // Initialize the plugin
                _plugin.Init(_startValue, _endValue, _relative);

                // If this is a From tween, swap start and end values
                if (_fromValue)
                {
                    (_startValue, _endValue) = (_endValue, _startValue);
                }

                // If relative, adjust endValue
                if (_relative)
                {
                    _endValue = Add(_startValue, _endValue);
                }
            }

            // Calculate progress
            float progress = Mathf.Clamp01((_data.ElapsedTime - _data.Delay) / _data.Duration);
            float easedProgress = _data.GetEasedProgress(progress);

            // Calculate interpolated value
            T value = _plugin.Lerp(_startValue, _endValue, easedProgress);

            // Apply value - make sure target still exists
            try 
            {
                _setter(value);
            }
            catch (MissingReferenceException) 
            {
                // Target has been destroyed, kill the tween
                Kill(false);
            }
        }

        // These methods need to be implemented for each value type
        protected virtual T Lerp(T a, T b, float t)
        {
            return _plugin.Lerp(a, b, t);
        }

        protected virtual T Add(T a, T b)
        {
            return _plugin.Add(a, b);
        }

        // Options

        public Tween<T> SetFrom()
        {
            _fromValue = true;
            _initialized = false;
            return this;
        }

        public Tween<T> SetFrom(T fromValue)
        {
            _startValue = fromValue;
            _initialized = true;
            return this;
        }

        public Tween<T> SetRelative(bool relative = true)
        {
            if (_initialized)
            {
                Debug.LogWarning("SetRelative should be called before the tween starts");
                return this;
            }

            _relative = relative;
            return this;
        }

        // Option setting should be passed to plugin
        public Tween<T> SetOptions(object options)
        {
            _plugin.SetOptions(options);
            return this;
        }
        
        
        
    }
}