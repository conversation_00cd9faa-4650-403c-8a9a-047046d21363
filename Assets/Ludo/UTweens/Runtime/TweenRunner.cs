using System.Collections.Generic;
using UnityEngine;

namespace Ludo.UTweens
{
    /// <summary>
    /// Handles the actual updating of all tweens
    /// </summary>
    public class TweenRunner : MonoBehaviour
    {
        private readonly List<ITween> _normalUpdateTweens = new List<ITween>();
        private readonly List<ITween> _lateUpdateTweens = new List<ITween>();
        private readonly List<ITween> _fixedUpdateTweens = new List<ITween>();
        
        private void Update()
        {
            ProcessTweens(_normalUpdateTweens, Time.deltaTime, Time.unscaledDeltaTime);
        }
        
        private void LateUpdate()
        {
            ProcessTweens(_lateUpdateTweens, Time.deltaTime, Time.unscaledDeltaTime);
        }
        
        private void FixedUpdate()
        {
            ProcessTweens(_fixedUpdateTweens, Time.fixedDeltaTime, Time.fixedUnscaledDeltaTime);
        }
        
        public void ManualUpdate(float deltaTime, float unscaledDeltaTime)
        {
            ProcessTweens(_normalUpdateTweens, deltaTime, unscaledDeltaTime);
            ProcessTweens(_lateUpdateTweens, deltaTime, unscaledDeltaTime);
            ProcessTweens(_fixedUpdateTweens, deltaTime, unscaledDeltaTime);
        }
        
        private void ProcessTweens(List<ITween> tweens, float deltaTime, float unscaledDeltaTime)
        {
            for (int i = tweens.Count - 1; i >= 0; i--)
            {
                ITween tween = tweens[i];
        
                if (tween == null)
                {
                    tweens.RemoveAt(i);
                    continue;
                }
        
                if (!tween.Active) continue;
        
                try
                {
                    float dt = tween.TimeScaleIndependent ? unscaledDeltaTime : deltaTime;
                    tween.Update(dt);
                }
                catch (MissingReferenceException)
                {
                    // Target object was destroyed - remove the tween
                    tweens.RemoveAt(i);
                }
            }
        }
        
        // Add to TweenRunner.cs
        private void OnDestroy()
        {
            // Kill all tweens when the runner is destroyed
            for (int i = _normalUpdateTweens.Count - 1; i >= 0; i--)
            {
                if (_normalUpdateTweens[i] != null)
                    _normalUpdateTweens[i].Kill(false);
            }
    
            for (int i = _lateUpdateTweens.Count - 1; i >= 0; i--)
            {
                if (_lateUpdateTweens[i] != null)
                    _lateUpdateTweens[i].Kill(false);
            }
    
            for (int i = _fixedUpdateTweens.Count - 1; i >= 0; i--)
            {
                if (_fixedUpdateTweens[i] != null)
                    _fixedUpdateTweens[i].Kill(false);
            }
    
            _normalUpdateTweens.Clear();
            _lateUpdateTweens.Clear();
            _fixedUpdateTweens.Clear();
        }
        
        internal void RegisterTween(ITween tween, UpdateType updateType)
        {
            switch (updateType)
            {
                case UpdateType.Normal:
                    _normalUpdateTweens.Add(tween);
                    break;
                case UpdateType.Late:
                    _lateUpdateTweens.Add(tween);
                    break;
                case UpdateType.Fixed:
                    _fixedUpdateTweens.Add(tween);
                    break;
            }
        }
        
        internal void UnregisterTween(ITween tween, UpdateType updateType)
        {
            switch (updateType)
            {
                case UpdateType.Normal:
                    _normalUpdateTweens.Remove(tween);
                    break;
                case UpdateType.Late:
                    _lateUpdateTweens.Remove(tween);
                    break;
                case UpdateType.Fixed:
                    _fixedUpdateTweens.Remove(tween);
                    break;
            }
        }
    }
}