using System;
using Ludo.UTweens.Plugins;
using UnityEngine;

namespace Ludo.UTweens
{
    /// <summary>
    /// Utility class for creating virtual tweens that don't target specific object properties
    /// but instead provide callbacks with interpolated values.
    /// </summary>
    public static class UTweenVirtual
    {
        /// <summary>
        /// Creates a virtual float tween that updates a callback with interpolated values.
        /// </summary>
        /// <param name="from">Starting value</param>
        /// <param name="to">Target value</param>
        /// <param name="duration">Duration in seconds</param>
        /// <param name="onUpdate">Callback that receives the current value at each update</param>
        /// <returns>A Tween that can be further configured</returns>
        public static Tween<float> Float(float from, float to, float duration, Action<float> onUpdate)
        {
            if (onUpdate == null) throw new ArgumentNullException(nameof(onUpdate));

            float value = from;
            return UTween.To(
                () => value,
                newValue =>
                {
                    value = newValue;
                    onUpdate(value);
                },
                to,
                duration
            );
        }

        /// <summary>
        /// Creates a virtual double tween that updates a callback with interpolated values.
        /// </summary>
        /// <param name="from">Starting value</param>
        /// <param name="to">Target value</param>
        /// <param name="duration">Duration in seconds</param>
        /// <param name="onUpdate">Callback that receives the current value at each update</param>
        /// <returns>A Tween that can be further configured</returns>
        public static Tween<double> Double(double from, double to, float duration, Action<double> onUpdate)
        {
            if (onUpdate == null) throw new ArgumentNullException(nameof(onUpdate));

            double value = from;
            return UTween.To(
                () => value,
                newValue =>
                {
                    value = newValue;
                    onUpdate(value);
                },
                to,
                duration
            );
        }

        /// <summary>
        /// Creates a virtual int tween that updates a callback with interpolated values.
        /// </summary>
        /// <param name="from">Starting value</param>
        /// <param name="to">Target value</param>
        /// <param name="duration">Duration in seconds</param>
        /// <param name="onUpdate">Callback that receives the current value at each update</param>
        /// <returns>A Tween that can be further configured</returns>
        public static Tween<int> Int(int from, int to, float duration, Action<int> onUpdate)
        {
            if (onUpdate == null) throw new ArgumentNullException(nameof(onUpdate));

            int value = from;
            return UTween.To(
                () => value,
                newValue =>
                {
                    value = newValue;
                    onUpdate(value);
                },
                to,
                duration
            );
        }

        /// <summary>
        /// Creates a virtual color tween that updates a callback with interpolated colors.
        /// </summary>
        /// <param name="from">Starting color</param>
        /// <param name="to">Target color</param>
        /// <param name="duration">Duration in seconds</param>
        /// <param name="onUpdate">Callback that receives the current color at each update</param>
        /// <returns>A Tween that can be further configured</returns>
        public static Tween<Color> Color(Color from, Color to, float duration, Action<Color> onUpdate)
        {
            if (onUpdate == null) throw new ArgumentNullException(nameof(onUpdate));

            Color value = from;
            return UTween.To(
                () => value,
                newValue =>
                {
                    value = newValue;
                    onUpdate(value);
                },
                to,
                duration
            );
        }

        /// <summary>
        /// Creates a delayed call tween that executes a callback after a specified delay.
        /// </summary>
        /// <param name="delay">Delay in seconds before the callback is executed</param>
        /// <param name="onComplete">Callback to execute when the delay completes</param>
        /// <returns>A Tween that can be further configured</returns>
        public static Tween<float> DelayedCall(float delay, Action onComplete)
        {
            if (onComplete == null) throw new ArgumentNullException(nameof(onComplete));

            float dummyValue = 0f;

            // Create a dummy tween with minimal duration
            var tween = UTween.To(
                () => dummyValue,
                x => dummyValue = x,
                1f, // Target value doesn't matter
                0.01f // Very small duration
            );

            // Set the delay and add completion callback
            tween.SetDelay(delay)
                .OnComplete(_ => onComplete());

            return tween;
        }

        public static Tween<string> String(string startValue, string endValue, float duration, Action<string> onUpdate,
            bool richText = false, ScrambleMode scrambleMode = ScrambleMode.None)
        {
            if (onUpdate == null) throw new ArgumentNullException(nameof(onUpdate));
            
            string currentValue = startValue;
            return UTween.To(
                () => currentValue,
                newValue =>
                {
                    currentValue = newValue;
                    onUpdate(currentValue);
                },
                endValue,
                duration
            ).SetOptions(new StringTweenOptions 
            { 
                RichTextEnabled = richText,
                ScrambleMode = scrambleMode
            });
        }
    }
}