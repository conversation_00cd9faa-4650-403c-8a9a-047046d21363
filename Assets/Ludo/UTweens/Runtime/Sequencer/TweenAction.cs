using UnityEngine;

namespace Ludo.UTweens.Runtime.Sequencer
{
    public abstract class TweenAction : MonoBeh<PERSON>our
    {
        [<PERSON><PERSON>("Common Settings")]
        [Tooltip("Delay before this specific action starts within the sequence.")]
        public float Delay = 0.0f;
        public float Duration = 1.0f;
        public Ease EaseType = Ease.OutQuad;

        /// <summary>
        /// This method will be implemented by each specific action
        /// to create its tween and add it to the main sequence.
        /// </summary>
        /// <param name="sequence">The sequence to add the tween/interval to.</param>
        public abstract void AddToSequence(Sequence sequence);
    }
}