using UnityEngine;
using System.Collections.Generic;
using Ludo.UTweens;
using Ludo.UTweens.Runtime.Sequencer;

namespace Ludo.UTweens
{
    /// <summary>
    /// Factory class for creating common TweenAction components easily
    /// </summary>
    public static class TweenActionFactory
    {
        /// <summary>
        /// Creates a Move action setup with the specified parameters
        /// </summary>
        public static MoveAction CreateMoveAction(GameObject parent, Vector3 targetPosition, float duration, 
            float delay = 0f, Ease easeType = Ease.OutQuad, bool isLocalMove = false)
        {
            MoveAction action = parent.AddComponent<MoveAction>();
            action.TargetPosition = targetPosition;
            action.Duration = duration;
            action.Delay = delay;
            action.EaseType = easeType;
            action.IsLocalMove = isLocalMove;
            return action;
        }

        /// <summary>
        /// Creates a Rotate action setup with the specified parameters
        /// </summary>
        public static RotateAction CreateRotateAction(GameObject parent, Vector3 targetRotation, float duration, 
            float delay = 0f, Ease easeType = Ease.OutQuad, bool isLocalRotation = false)
        {
            RotateAction action = parent.AddComponent<RotateAction>();
            action.TargetRotation = targetRotation;
            action.Duration = duration;
            action.Delay = delay;
            action.EaseType = easeType;
            action.IsLocalRotation = isLocalRotation;
            return action;
        }

        /// <summary>
        /// Creates a Scale action setup with the specified parameters
        /// </summary>
        public static ScaleAction CreateScaleAction(GameObject parent, Vector3 targetScale, float duration, 
            float delay = 0f, Ease easeType = Ease.OutQuad)
        {
            ScaleAction action = parent.AddComponent<ScaleAction>();
            action.TargetScale = targetScale;
            action.Duration = duration;
            action.Delay = delay;
            action.EaseType = easeType;
            return action;
        }

        /// <summary>
        /// Creates a Color Change action setup with the specified parameters
        /// </summary>
        public static ColorChangeAction CreateColorChangeAction(GameObject parent, Color targetColor, float duration, 
            float delay = 0f, Ease easeType = Ease.OutQuad)
        {
            ColorChangeAction action = parent.AddComponent<ColorChangeAction>();
            action.TargetColor = targetColor;
            action.Duration = duration;
            action.Delay = delay;
            action.EaseType = easeType;
            return action;
        }

        /// <summary>
        /// Creates a fade action for CanvasGroup
        /// </summary>
        public static CanvasGroupFadeAction CreateFadeAction(GameObject parent, float targetAlpha, float duration, 
            float delay = 0f, Ease easeType = Ease.OutQuad)
        {
            CanvasGroupFadeAction action = parent.AddComponent<CanvasGroupFadeAction>();
            action.TargetAlpha = targetAlpha;
            action.Duration = duration;
            action.Delay = delay;
            action.EaseType = easeType;
            return action;
        }

        /// <summary>
        /// Creates a Callback action setup with the specified parameters
        /// </summary>
        public static CallbackAction CreateCallbackAction(GameObject parent, UnityEngine.Events.UnityAction callback, 
            float delay = 0f, bool waitAfterCallback = false, float waitDuration = 0f)
        {
            CallbackAction action = parent.AddComponent<CallbackAction>();
            action.OnCallback = new UnityEngine.Events.UnityEvent();
            action.OnCallback.AddListener(callback);
            action.Delay = delay;
            action.WaitAfterCallback = waitAfterCallback;
            action.Duration = waitDuration;
            return action;
        }

        /// <summary>
        /// Creates a Shake action setup with the specified parameters
        /// </summary>
        public static ShakeAction CreateShakeAction(GameObject parent, ShakeAction.ShakeType type, Vector3 strength, 
            float duration, float delay = 0f, int vibrato = 10, float randomness = 90f, bool fadeOut = true)
        {
            ShakeAction action = parent.AddComponent<ShakeAction>();
            action.Type = type;
            action.Strength = strength;
            action.Duration = duration;
            action.Delay = delay;
            action.Vibrato = vibrato;
            action.Randomness = randomness;
            action.FadeOut = fadeOut;
            return action;
        }

        /// <summary>
        /// Creates a Punch action setup with the specified parameters
        /// </summary>
        public static PunchAction CreatePunchAction(GameObject parent, PunchAction.PunchType type, Vector3 strength, 
            float duration, float delay = 0f, int vibrato = 10, float elasticity = 1.0f)
        {
            PunchAction action = parent.AddComponent<PunchAction>();
            action.Type = type;
            action.Strength = strength;
            action.Duration = duration;
            action.Delay = delay;
            action.Vibrato = vibrato;
            action.Elasticity = elasticity;
            return action;
        }

        /// <summary>
        /// Creates a Parallel action with the specified child actions
        /// </summary>
        public static ParallelAction CreateParallelAction(GameObject parent, List<TweenAction> actions, 
            float delay = 0f, Ease easeType = Ease.OutQuad, bool useMaxDuration = true)
        {
            ParallelAction action = parent.AddComponent<ParallelAction>();
            action.ParallelActions = actions;
            action.Delay = delay;
            action.EaseType = easeType;
            action.UseMaxDuration = useMaxDuration;
            return action;
        }

        /// <summary>
        /// Creates a Text Animation action setup with the specified parameters
        /// </summary>
        public static TextAnimationAction CreateTextAnimationAction(GameObject parent, string targetText, 
            TextAnimationAction.TextAnimationType type = TextAnimationAction.TextAnimationType.CharByChar, 
            float duration = 1.0f, float delay = 0f, Ease easeType = Ease.OutQuad)
        {
            TextAnimationAction action = parent.AddComponent<TextAnimationAction>();
            action.TargetText = targetText;
            action.AnimationType = type;
            action.Duration = duration;
            action.Delay = delay;
            action.EaseType = easeType;
            return action;
        }

        /// <summary>
        /// Creates a RectTransform action setup with the specified parameters
        /// </summary>
        public static RectTransformAction CreateRectTransformAction(GameObject parent, Vector2 targetValue, 
            RectTransformAction.RectTransformProperty property = RectTransformAction.RectTransformProperty.AnchoredPosition, 
            float duration = 1.0f, float delay = 0f, Ease easeType = Ease.OutQuad)
        {
            RectTransformAction action = parent.AddComponent<RectTransformAction>();
            action.TargetValue = targetValue;
            action.Property = property;
            action.Duration = duration;
            action.Delay = delay;
            action.EaseType = easeType;
            return action;
        }
    }
}