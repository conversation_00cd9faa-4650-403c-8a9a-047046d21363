using UnityEngine;
using UnityEngine.UI;

namespace Ludo.UTweens.Runtime.Sequencer
{
    public class ProgressBarAction : TweenAction
    {
        public enum ProgressBarType { Image, Slider, Custom }

        [Header("Progress Bar Settings")]
        [Tooltip("Type of progress bar")]
        public ProgressBarType BarType = ProgressBarType.Image;
    
        [Tooltip("Target Image (if using Image type)")]
        public Image TargetImage;
    
        [Tooltip("Target Slider (if using Slider type)")]
        public Slider TargetSlider;
    
        [Tooltip("Start value (0-1)")]
        [Range(0f, 1f)]
        public float StartValue = 0f;
    
        [Tooltip("End value (0-1)")]
        [Range(0f, 1f)]
        public float EndValue = 1f;
    
        [Tooltip("Custom getter (for Custom type)")]
        [SerializeField]
        private UnityEngine.Events.UnityEvent<float> OnValueChanged;
    
        [Tooltip("If true, plays particle effects on completion")]
        public bool PlayEffectOnComplete = false;
    
        [Tooltip("Particle system to activate on completion")]
        public ParticleSystem CompletionEffect;

        public override void AddToSequence(Sequence sequence)
        {
            if (Delay > 0)
                sequence.AppendInterval(Delay);
            
            ITween progressTween = UTweenVirtual.Float(StartValue, EndValue, Duration, t => {
                switch (BarType)
                {
                    case ProgressBarType.Image:
                        if (TargetImage != null)
                            TargetImage.fillAmount = t;
                        break;
                    
                    case ProgressBarType.Slider:
                        if (TargetSlider != null)
                            TargetSlider.value = t;
                        break;
                    
                    case ProgressBarType.Custom:
                        OnValueChanged?.Invoke(t);
                        break;
                }
            });
        
            if (PlayEffectOnComplete && CompletionEffect != null)
            {
                progressTween.OnComplete(_ => {
                    CompletionEffect.Play();
                });
            }
            
            progressTween.SetEase(EaseType);
            sequence.Append(progressTween);
        }
    }
}