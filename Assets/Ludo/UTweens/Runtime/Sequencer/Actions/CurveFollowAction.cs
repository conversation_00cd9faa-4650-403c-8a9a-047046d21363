using UnityEngine;

namespace Ludo.UTweens.Runtime.Sequencer
{
    public class CurveFollowAction : TweenAction
    {
        [<PERSON><PERSON>("Curve Follow Settings")]
        [Tooltip("The Transform to move along the curve. If null, uses this GameObject's Transform.")]
        public Transform TargetTransform;
    
        [Tooltip("Animation curve for X movement")]
        public AnimationCurve XCurve = AnimationCurve.Linear(0, 0, 1, 0);
    
        [<PERSON>lt<PERSON>("Animation curve for Y movement")]
        public AnimationCurve YCurve = AnimationCurve.Linear(0, 0, 1, 0); 
    
        [Tooltip("Animation curve for Z movement")]
        public AnimationCurve ZCurve = AnimationCurve.Linear(0, 0, 1, 0);
    
        [Tooltip("Scale of the curve movement")]
        public Vector3 CurveScale = Vector3.one;
    
        [Tooltip("If true, the curve is relative to starting position")]
        public bool RelativeToStart = true;
    
        [Tooltip("If true, uses local space instead of world space")]
        public bool UseLocalSpace = false;
    
        [Tooltip("If true, object will orient to face the direction of movement")]
        public bool OrientToPath = false;

        public override void AddToSequence(Sequence sequence)
        {
            if (TargetTransform == null)
                TargetTransform = transform;
            
            if (TargetTransform == null)
            {
                Debug.LogWarning($"CurveFollowAction on {gameObject.name} has no target Transform.", this);
                return;
            }

            if (Delay > 0)
                sequence.AppendInterval(Delay);
            
            // Store starting position
            Vector3 startPos = UseLocalSpace ? TargetTransform.localPosition : TargetTransform.position;
            Vector3 lastPos = startPos;
            
            ITween curveTween = UTweenVirtual.Float(0, 1, Duration, t => {
                // Calculate position from curves
                Vector3 curveOffset = new Vector3(
                    XCurve.Evaluate(t) * CurveScale.x,
                    YCurve.Evaluate(t) * CurveScale.y,
                    ZCurve.Evaluate(t) * CurveScale.z
                );
            
                Vector3 targetPos = RelativeToStart ? startPos + curveOffset : curveOffset;
            
                // Update position
                if (UseLocalSpace)
                    TargetTransform.localPosition = targetPos;
                else
                    TargetTransform.position = targetPos;
                
                // Orient to path if needed
                if (OrientToPath)
                {
                    Vector3 currentPos = UseLocalSpace ? TargetTransform.localPosition : TargetTransform.position;
                    Vector3 direction = currentPos - lastPos;
                
                    if (direction.magnitude > 0.001f)
                    {
                        if (UseLocalSpace)
                        {
                            TargetTransform.localRotation = Quaternion.LookRotation(direction);
                        }
                        else
                        {
                            TargetTransform.rotation = Quaternion.LookRotation(direction);
                        }
                    }
                
                    lastPos = currentPos;
                }
            });
            
            curveTween.SetEase(EaseType);
            sequence.Append(curveTween);
        }
    }
}