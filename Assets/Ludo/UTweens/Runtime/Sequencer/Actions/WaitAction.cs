using UnityEngine;

namespace Ludo.UTweens.Runtime.Sequencer
{
    public class WaitAction : TweenAction
    {
        [<PERSON><PERSON>("Wait Action Settings")]
        [Tooltip("The duration to wait before continuing the sequence.")]
        public float WaitDuration = 1f;

        public override void AddToSequence(Sequence sequence)
        {
            if (Delay > 0)
            {
                sequence.AppendInterval(Delay);
            }

            // Append a wait interval to the sequence
            sequence.AppendInterval(WaitDuration);
        }
    }
}