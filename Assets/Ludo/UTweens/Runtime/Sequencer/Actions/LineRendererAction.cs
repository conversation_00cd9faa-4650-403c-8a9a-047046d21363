using UnityEngine;

namespace Ludo.UTweens.Runtime.Sequencer
{
    public class LineRendererAction : TweenAction
    {
        public enum LineProperty { Width, Color, Position, Points }
    
        [Header("Line Renderer Settings")]
        [Tooltip("The LineRenderer to modify. If null, uses this GameObject's LineRenderer.")]
        public LineRenderer TargetLine;
    
        [Tooltip("Property to animate")]
        public LineProperty Property = LineProperty.Width;
    
        [<PERSON><PERSON>("Width Settings")]
        [Tooltip("Start width")]
        public float StartWidth = 0.1f;
    
        [Tooltip("End width")]
        public float EndWidth = 0.1f;
    
        [Head<PERSON>("Color Settings")]
        [Tooltip("Start color")]
        public Color StartColor = Color.white;
    
        [Tooltip("End color")]
        public Color EndColor = Color.white;
    
        [Header("Position Settings")]
        [Tooltip("Target positions (for Position property)")]
        public Vector3[] TargetPositions;
    
        [Toolt<PERSON>("If true, will draw the line progressively")]
        public bool ProgressiveDraw = false;

        public override void AddToSequence(Sequence sequence)
        {
            if (TargetLine == null)
                TargetLine = GetComponent<LineRenderer>();
            
            if (TargetLine == null)
            {
                Debug.LogWarning($"LineRendererAction on {gameObject.name} has no target LineRenderer.", this);
                return;
            }

            if (Delay > 0)
                sequence.AppendInterval(Delay);
            
            ITween lineTween = null;
        
            switch (Property)
            {
                case LineProperty.Width:
                    lineTween = UTweenVirtual.Float(0, 1, Duration, t => {
                        TargetLine.startWidth = Mathf.Lerp(TargetLine.startWidth, StartWidth, t);
                        TargetLine.endWidth = Mathf.Lerp(TargetLine.endWidth, EndWidth, t);
                    });
                    break;
                
                case LineProperty.Color:
                    lineTween = UTweenVirtual.Float(0, 1, Duration, t => {
                        TargetLine.startColor = Color.Lerp(TargetLine.startColor, StartColor, t);
                        TargetLine.endColor = Color.Lerp(TargetLine.endColor, EndColor, t);
                    });
                    break;
                
                case LineProperty.Position:
                    if (TargetPositions != null && TargetPositions.Length > 0)
                    {
                        // Store original positions
                        Vector3[] originalPositions = new Vector3[TargetLine.positionCount];
                        for (int i = 0; i < TargetLine.positionCount; i++)
                            originalPositions[i] = TargetLine.GetPosition(i);  

                    
                        // Ensure arrays match
                        if (TargetPositions.Length != originalPositions.Length)
                        {
                            TargetLine.positionCount = TargetPositions.Length;
                            originalPositions = new Vector3[TargetPositions.Length];
                            for (int i = 0; i < Mathf.Min(TargetLine.positionCount, originalPositions.Length); i++)
                                originalPositions[i] = TargetLine.GetPosition(i);  // Fixed: GetPosition returns a Vector3
                        }
                    
                        lineTween = UTweenVirtual.Float(0, 1, Duration, t => {
                            for (int i = 0; i < TargetPositions.Length; i++)
                            {
                                Vector3 newPos = Vector3.Lerp(originalPositions[i], TargetPositions[i], t);
                                TargetLine.SetPosition(i, newPos);
                            }
                        });
                    }
                    break;
                
                case LineProperty.Points:
                    if (TargetPositions != null && TargetPositions.Length > 0 && ProgressiveDraw)
                    {
                        TargetLine.positionCount = TargetPositions.Length;
                    
                        lineTween = UTweenVirtual.Float(0, 1, Duration, t => {
                            int visiblePoints = Mathf.Max(2, Mathf.FloorToInt(t * TargetPositions.Length));
                        
                            // Show only the points up to the current progress
                            for (int i = 0; i < TargetPositions.Length; i++)
                            {
                                if (i < visiblePoints)
                                    TargetLine.SetPosition(i, TargetPositions[i]);
                                else
                                    TargetLine.SetPosition(i, TargetPositions[visiblePoints-1]); // Hide extra points
                            }
                        });
                    }
                    break;
            }
            
            if (lineTween != null)
            {
                lineTween.SetEase(EaseType);
                sequence.Append(lineTween);
            }
        }
    }
}