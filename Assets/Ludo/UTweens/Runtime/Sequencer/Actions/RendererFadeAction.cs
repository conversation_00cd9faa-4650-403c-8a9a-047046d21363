using Ludo.UTweens.Extensions;
using UnityEngine;

namespace Ludo.UTweens.Runtime.Sequencer
{
    public class RendererFadeAction : TweenAction
    {
        [Header("Renderer Fade Settings")]
        [Tooltip("The Renderer to fade. If null, uses this GameObject's Renderer.")]
        public Renderer TargetRenderer;
        public float TargetAlpha = 1.0f;
        [Tooltip("The material property name that contains the alpha. Default is _Color.")]
        public string AlphaPropertyName = "_Color";

        private Color _originalColor;

        void Awake()
        {
            if (TargetRenderer == null)
            {
                TargetRenderer = GetComponent<Renderer>();
            }
        }

        public override void AddToSequence(Sequence sequence)
        {
            if (TargetRenderer == null)
            {
                Debug.LogWarning($"RendererFadeAction on {gameObject.name} has no target Renderer.", this);
                return;
            }

            if (Delay > 0)
            {
                sequence.AppendInterval(Delay);
            }

            // Create a tween that only affects the alpha component
            ITween fadeTween = UTweenVirtual.Float(
                TargetRenderer.material.GetColor(AlphaPropertyName).a,
                TargetAlpha,
                Duration,
                alpha =>
                {
                    Color color = TargetRenderer.material.GetColor(AlphaPropertyName);
                    color.a = alpha;
                    TargetRenderer.material.SetColor(AlphaPropertyName, color);
                }
            );

            // Apply common settings
            fadeTween.SetEase(EaseType);

            // Append to the main sequence
            sequence.Append(fadeTween);
        }
    }
}