using Ludo.UTweens.Extensions;
using Ludo.UTweens.Plugins;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace Ludo.UTweens.Runtime.Sequencer
{
    public class TextAnimationAction : TweenAction
    {
        public enum TextAnimationType { Char<PERSON>y<PERSON>har, Counter, Scramble }

        [Header("Text Animation Settings")]
        public TextAnimationType AnimationType = TextAnimationType.CharByChar;
        
        [Tooltip("If true, preserves rich text tags during animation")]
        public bool PreserveRichText = true;
        
        [Tooltip("Target text to animate to")]
        public string TargetText = "Animated Text";
        
        [Header("Counter Settings")]
        [Tooltip("Start value for counter animation")]
        public int CounterStartValue = 0;
        
        [Tooltip("End value for counter animation")]
        public int CounterEndValue = 100;
        
        [Tooltip("Add thousands separator (e.g., 1,000)")]
        public bool ThousandsSeparator = true;
        
        [Header("Scramble Settings")]
        public ScrambleMode ScrambleMode = ScrambleMode.None;
        
        [Header("Target Settings (assign only one)")]
        public Text LegacyText;
        public TMP_Text TMProText;

        void Awake()
        {
            // Auto-detect a text component if none was assigned
            if (LegacyText == null && TMProText == null)
            {
                TMProText = GetComponent<TMP_Text>();
                if (TMProText == null)
                {
                    LegacyText = GetComponent<Text>();
                }
            }
        }

        public override void AddToSequence(Sequence sequence)
        {
            if (LegacyText == null && TMProText == null)
            {
                Debug.LogWarning($"TextAnimationAction on {gameObject.name} has no text component.", this);
                return;
            }

            if (Delay > 0)
            {
                sequence.AppendInterval(Delay);
            }

            ITween textTween = null;

            // Handle each animation type
            switch (AnimationType)
            {
                case TextAnimationType.CharByChar:
                    if (TMProText != null)
                    {
                        textTween = TMProText.UText(TargetText, Duration, PreserveRichText);
                    }
                    else
                    {
                        textTween = LegacyText.UText(TargetText, Duration, PreserveRichText);
                    }
                    break;
                    
                case TextAnimationType.Counter:
                    if (TMProText != null)
                    {
                        textTween = UTweenVirtual.Float(CounterStartValue, CounterEndValue, Duration, value =>
                        {
                            int intValue = Mathf.RoundToInt(value);
                            TMProText.text = ThousandsSeparator ? string.Format("{0:n0}", intValue) : intValue.ToString();
                        });
                    }
                    else
                    {
                        textTween = LegacyText.UCounter(CounterStartValue, CounterEndValue, Duration, ThousandsSeparator);
                    }
                    break;
                    
                case TextAnimationType.Scramble:
                    string startText;
                    if (TMProText != null)
                    {
                        startText = TMProText.text;
                        textTween = TMProText.UText(TargetText, Duration, PreserveRichText, ScrambleMode);
                    }
                    else
                    {
                        startText = LegacyText.text;
                        textTween = LegacyText.UText(TargetText, Duration, PreserveRichText, ScrambleMode);
                    }
                    break;
            }

            // Apply common settings
            textTween.SetEase(EaseType);

            // Append to the main sequence
            sequence.Append(textTween);
        }
    }
}