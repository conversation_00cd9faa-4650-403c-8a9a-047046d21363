using Ludo.UTweens.Extensions;
using UnityEngine;

namespace Ludo.UTweens.Runtime.Sequencer
{
    public class MaterialPropertyAction : TweenAction
    {
        public enum PropertyType { Float, Color, Vector, Offset, Tiling }

        [Header("Material Property Settings")]
        [Tooltip("The Renderer to modify. If null, uses this GameObject's Renderer.")]
        public Renderer TargetRenderer;
        
        [Tooltip("If specified, will use this material directly instead of one from TargetRenderer")]
        public Material TargetMaterial;
        
        [Tooltip("Which type of property to animate")]
        public PropertyType Type = PropertyType.Float;
        
        [Tooltip("The name of the material property (shader parameter)")]
        public string PropertyName = "_Metallic";
        
        [Header("Target Values")]
        [Tooltip("Target float value (used if Type is Float)")]
        public float TargetFloat = 1.0f;
        
        [Tooltip("Target color value (used if Type is Color)")]
        public Color TargetColor = Color.white;
        
        [Tooltip("Target vector value (used if Type is Vector, Offset, or Tiling)")]
        public Vector4 TargetVector = Vector4.one;

        void Awake()
        {
            if (TargetRenderer == null && TargetMaterial == null)
            {
                TargetRenderer = GetComponent<Renderer>();
            }
        }

        public override void AddToSequence(Sequence sequence)
        {
            // Make sure we have either a renderer or a material
            if (TargetRenderer == null && TargetMaterial == null)
            {
                Debug.LogWarning($"MaterialPropertyAction on {gameObject.name} has no target Renderer or Material.", this);
                return;
            }

            if (Delay > 0)
            {
                sequence.AppendInterval(Delay);
            }

            // Get the material to work with
            Material material = TargetMaterial != null ? TargetMaterial : TargetRenderer.material;
            ITween materialTween = null;
            
            // Create tween based on property type
            switch (Type)
            {
                case PropertyType.Float:
                    materialTween = UTween.To(
                        () => material.GetFloat(PropertyName),
                        x => material.SetFloat(PropertyName, x),
                        TargetFloat,
                        Duration
                    );
                    break;
                    
                case PropertyType.Color:
                    materialTween = UTween.To(
                        () => material.GetColor(PropertyName),
                        x => material.SetColor(PropertyName, x),
                        TargetColor,
                        Duration
                    );
                    break;
                    
                case PropertyType.Vector:
                    materialTween = UTween.To(
                        () => material.GetVector(PropertyName),
                        x => material.SetVector(PropertyName, x),
                        TargetVector,
                        Duration
                    );
                    break;
                    
                case PropertyType.Offset:
                    materialTween = UTween.To(
                        () => material.GetTextureOffset(PropertyName),
                        x => material.SetTextureOffset(PropertyName, x),
                        new Vector2(TargetVector.x, TargetVector.y),
                        Duration
                    );
                    break;
                    
                case PropertyType.Tiling:
                    materialTween = UTween.To(
                        () => material.GetTextureScale(PropertyName),
                        x => material.SetTextureScale(PropertyName, x),
                        new Vector2(TargetVector.x, TargetVector.y),
                        Duration
                    );
                    break;
            }

            // Apply common settings
            materialTween.SetEase(EaseType);
            
            // Set target for identification
            materialTween.SetTarget(material);

            // Append to the main sequence
            sequence.Append(materialTween);
        }
    }
}