using Ludo.UTweens.Extensions;
using UnityEngine;

namespace Ludo.UTweens.Runtime.Sequencer
{
    public class ShakeAction : TweenAction
    {
        public enum ShakeType { Position, Rotation, Scale }

        [Header("Shake Action Settings")]
        [Tooltip("The Transform to affect. If null, uses this GameObject's Transform.")]
        public Transform TargetTransform;
        
        [Tooltip("Type of shake animation to perform")]
        public ShakeType Type = ShakeType.Position;
        
        [<PERSON>lt<PERSON>("Strength of the shake effect")]
        public Vector3 Strength = new Vector3(0.5f, 0.5f, 0);
        
        [Tooltip("Number of vibrations/oscillations")]
        public int Vibrato = 10;
        
        [Tooltip("Randomness of the shake (0-180)")]
        [Range(0, 180)]
        public float Randomness = 90.0f;
        
        [Tooltip("Should position values be rounded to integers?")]
        public bool Snapping = false;
        
        [Tooltip("Should the shake fade out?")]
        public bool FadeOut = true;

        void Awake()
        {
            if (TargetTransform == null)
            {
                TargetTransform = transform;
            }
        }

        public override void AddToSequence(Sequence sequence)
        {
            if (TargetTransform == null && !TryGetComponent(out TargetTransform))
            {
                Debug.LogWarning($"ShakeAction on {gameObject.name} has no target Transform.", this);
                return;
            }

            if (Delay > 0)
            {
                sequence.AppendInterval(Delay);
            }

            // Create the specific shake sequence based on the type
            Sequence shakeSequence = null;
            
            switch (Type)
            {
                case ShakeType.Position:
                    shakeSequence = TargetTransform.UShakePosition(Duration, Strength, Vibrato, Randomness, Snapping, FadeOut);
                    break;
                    
                case ShakeType.Rotation:
                    shakeSequence = TargetTransform.UShakeRotation(Duration, Strength, Vibrato, Randomness, FadeOut);
                    break;
                    
                case ShakeType.Scale:
                    shakeSequence = TargetTransform.UShakeScale(Duration, Strength, Vibrato, FadeOut);
                    break;
            }

            // The ease type is already built into the shake itself, so we don't need to set it again

            // Append to the main sequence
            sequence.Append(shakeSequence);
        }
    }
}