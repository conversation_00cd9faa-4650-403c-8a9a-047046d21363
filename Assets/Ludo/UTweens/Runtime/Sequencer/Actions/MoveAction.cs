using Ludo.UTweens.Extensions;
using UnityEngine;

namespace Ludo.UTweens.Runtime.Sequencer
{
    public class MoveAction : TweenAction
    {
        [Header("Move Action Settings")]
        [Tooltip("The Transform to move. If null, uses this GameObject's Transform.")]
        public Transform TargetTransform;
        public Vector3 TargetPosition;
        public bool IsLocalMove = false;

        // Automatically assign target if null
        void Awake()
        {
            if (TargetTransform == null)
            {
                TargetTransform = transform;
            }
        }

        public override void AddToSequence(Sequence sequence)
        {
            if (TargetTransform == null && !TryGetComponent(out TargetTransform))
            {
                Debug.LogWarning($"MoveAction on {gameObject.name} has no target Transform.", this);
                return;
            }
            
            if (Delay > 0)
            {
                sequence.AppendInterval(Delay); 
            }

            // Create the specific tween
            ITween moveTween = IsLocalMove
                ? TargetTransform.ULocalMove(TargetPosition, Duration) 
                : TargetTransform.UMove(TargetPosition, Duration); 

            // Apply common settings
            moveTween.SetEase(EaseType);

            // Append to the main sequence
            sequence.Append(moveTween); 
        }
    }
}