using UnityEngine;

namespace Ludo.UTweens.Runtime.Sequencer
{
    public class SpriteFadeAction : TweenAction
    {
        [<PERSON><PERSON>("Sprite Fade Settings")]
        [Tooltip("The SpriteRenderer to modify. If null, uses this GameObject's SpriteRenderer.")]
        public SpriteRenderer TargetRenderer;
    
        [Tooltip("Target alpha value")]
        [Range(0f, 1f)]
        public float TargetAlpha = 1.0f;
    
        [<PERSON>lt<PERSON>("If true, will also change the sprite")]
        public bool ChangeSprite = false;
    
        [Tooltip("Target sprite (if ChangeSprite is true)")]
        public Sprite TargetSprite;
    
        [Tooltip("How to change the sprite")]
        public enum SpriteChangeType { Instant, CrossFade }
    
        [Toolt<PERSON>("How to change the sprite")]
        public SpriteChangeType ChangeType = SpriteChangeType.CrossFade;

        public override void AddToSequence(Sequence sequence)
        {
            if (TargetRenderer == null)
                TargetRenderer = GetComponent<SpriteRenderer>();
            
            if (TargetRenderer == null)
            {
                Debug.LogWarning($"SpriteFadeAction on {gameObject.name} has no target SpriteRenderer.", this);
                return;
            }

            if (Delay > 0)
                sequence.AppendInterval(Delay);
            
            if (ChangeSprite && TargetSprite != null)
            {
                if (ChangeType == SpriteChangeType.Instant)
                {
                    // Just change the sprite and fade alpha
                    sequence.AppendCallback(() => {
                        TargetRenderer.sprite = TargetSprite;
                    });
                
                    ITween fadeTween = UTween.To(
                        () => TargetRenderer.color.a,
                        a => {
                            Color c = TargetRenderer.color;
                            c.a = a;
                            TargetRenderer.color = c;
                        },
                        TargetAlpha,
                        Duration
                    );
                
                    fadeTween.SetEase(EaseType);
                    sequence.Append(fadeTween);
                }
                else // CrossFade
                {
                    // For crossfade, we need to create a temporary sprite renderer
                    sequence.AppendCallback(() => {
                        // Create a game object with a sprite renderer
                        GameObject tempObj = new GameObject("TempSprite");
                        tempObj.transform.SetParent(TargetRenderer.transform.parent);
                        tempObj.transform.position = TargetRenderer.transform.position;
                        tempObj.transform.rotation = TargetRenderer.transform.rotation;
                        tempObj.transform.localScale = TargetRenderer.transform.localScale;
                    
                        // Add a sprite renderer with the target sprite
                        SpriteRenderer tempRenderer = tempObj.AddComponent<SpriteRenderer>();
                        tempRenderer.sprite = TargetSprite;
                        tempRenderer.sortingLayerID = TargetRenderer.sortingLayerID;
                        tempRenderer.sortingOrder = TargetRenderer.sortingOrder + 1;
                    
                        // Start fully transparent
                        Color tempColor = TargetRenderer.color;
                        tempColor.a = 0;
                        tempRenderer.color = tempColor;
                    
                        // Create a sequence for the crossfade
                        Sequence crossFadeSequence = UTween.Sequence();
                    
                        // Fade out original sprite
                        crossFadeSequence.Append(UTween.To(
                            () => TargetRenderer.color.a,
                            a => {
                                Color c = TargetRenderer.color;
                                c.a = a;
                                TargetRenderer.color = c;
                            },
                            0f,
                            Duration * 0.5f
                        ));
                    
                        // Fade in new sprite
                        crossFadeSequence.Join(UTween.To(
                            () => tempRenderer.color.a,
                            a => {
                                Color c = tempRenderer.color;
                                c.a = a;
                                tempRenderer.color = c;
                            },
                            TargetAlpha,
                            Duration * 0.5f
                        ));
                    
                        // Complete the crossfade
                        crossFadeSequence.OnComplete(_ => {
                            // Copy the temp renderer's properties to the original
                            TargetRenderer.sprite = TargetSprite;
                            Color finalColor = TargetRenderer.color;
                            finalColor.a = TargetAlpha;
                            TargetRenderer.color = finalColor;
                        
                            // Destroy the temporary object
                            GameObject.Destroy(tempObj);
                        });
                    
                        // Apply the sequence's ease type
                        crossFadeSequence.SetEase(EaseType);
                    
                        // Add it to the main sequence
                        sequence.Append(crossFadeSequence);
                    });
                }
            }
            else
            {
                // Just fade the alpha
                ITween fadeTween = UTween.To(
                    () => TargetRenderer.color.a,
                    a => {
                        Color c = TargetRenderer.color;
                        c.a = a;
                        TargetRenderer.color = c;
                    },
                    TargetAlpha,
                    Duration
                );
            
                fadeTween.SetEase(EaseType);
                sequence.Append(fadeTween);
            }
        }
    }
}