using Ludo.UTweens.Extensions;
using Ludo.UTweens.Plugins;
using UnityEngine;

namespace Ludo.UTweens.Runtime.Sequencer
{
    public class ScaleAction : TweenAction
    {
        [Header("Scale Action Settings")]
        [Tooltip("The Transform to scale. If null, uses this GameObject's Transform.")]
        public Transform TargetTransform;
        public Vector3 TargetScale = Vector3.one;
        public bool Snapping = false;
        [Tooltip("Constrain scaling to specific axes")]
        public AxisConstraint AxisConstraint = AxisConstraint.None;

        // Automatically assign target if null
        void Awake()
        {
            if (TargetTransform == null)
            {
                TargetTransform = transform;
            }
        }

        public override void AddToSequence(Sequence sequence)
        {
            if (TargetTransform == null && !TryGetComponent(out TargetTransform))
            {
                Debug.LogWarning($"ScaleAction on {gameObject.name} has no target Transform.", this);
                return;
            }
            
            if (Delay > 0)
            {
                sequence.AppendInterval(Delay); 
            }

            // Create the scale tween with options
            ITween scaleTween = TargetTransform.UScale(TargetScale, Duration, AxisConstraint, Snapping);

            // Apply common settings
            scaleTween.SetEase(EaseType);

            // Append to the main sequence
            sequence.Append(scaleTween);
        }
    }
}