using Ludo.UTweens.Extensions;
using UnityEngine;

namespace Ludo.UTweens.Runtime.Sequencer
{
    public class PunchAction : TweenAction
    {
        public enum PunchType { Position, Rotation, Scale }

        [Header("Punch Action Settings")]
        [Tooltip("The Transform to affect. If null, uses this GameObject's Transform.")]
        public Transform TargetTransform;
        
        [Tooltip("Type of punch animation to perform")]
        public PunchType Type = PunchType.Position;
        
        [Toolt<PERSON>("Strength of the punch effect")]
        public Vector3 Strength = new Vector3(0.5f, 0, 0);
        
        [Tooltip("Number of vibrations/oscillations")]
        public int Vibrato = 10;
        
        [Tooltip("Elasticity of the punch (0 = no bounce back, 1 = full bounce back)")]
        [Range(0, 1)]
        public float Elasticity = 1.0f;
        
        [Tooltip("Should position values be rounded to integers?")]
        public bool Snapping = false;

        void Awake()
        {
            if (TargetTransform == null)
            {
                TargetTransform = transform;
            }
        }

        public override void AddToSequence(Sequence sequence)
        {
            if (TargetTransform == null && !TryGetComponent(out TargetTransform))
            {
                Debug.LogWarning($"PunchAction on {gameObject.name} has no target Transform.", this);
                return;
            }

            if (Delay > 0)
            {
                sequence.AppendInterval(Delay);
            }

            // Create the specific punch sequence based on the type
            Sequence punchSequence = null;
            
            switch (Type)
            {
                case PunchType.Position:
                    punchSequence = TargetTransform.UPunchPosition(Strength, Duration, Vibrato, Elasticity, Snapping);
                    break;
                    
                case PunchType.Rotation:
                    punchSequence = TargetTransform.UPunchRotation(Strength, Duration, Vibrato, Elasticity);
                    break;
                    
                case PunchType.Scale:
                    punchSequence = TargetTransform.UPunchScale(Strength, Duration, Vibrato, Elasticity);
                    break;
            }

            // Apply common settings
            punchSequence.SetEase(EaseType);

            // Append to the main sequence
            sequence.Append(punchSequence);
        }
    }
}