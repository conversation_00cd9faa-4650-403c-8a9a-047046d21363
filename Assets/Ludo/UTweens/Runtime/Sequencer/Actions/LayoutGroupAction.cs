using UnityEngine;
using UnityEngine.UI;

namespace Ludo.UTweens.Runtime.Sequencer
{
    public class LayoutGroupAction : TweenAction
    {
        public enum LayoutProperty { Spacing, Padding }
    
        [Header("Layout Group Settings")]
        [Tooltip("The LayoutGroup to modify. If null, uses this GameObject's LayoutGroup.")]
        public LayoutGroup TargetLayout;
    
        [Tooltip("Property to animate")]
        public LayoutProperty Property = LayoutProperty.Spacing;
    
        [Tooltip("Target spacing value (for HorizontalLayoutGroup or VerticalLayoutGroup)")]
        public float TargetSpacing = 0f;
    
        [Tooltip("Target padding values")]
        public RectOffset TargetPadding = new RectOffset();

        public override void AddToSequence(Sequence sequence)
        {
            if (TargetLayout == null)
                TargetLayout = GetComponent<LayoutGroup>();
            
            if (TargetLayout == null)
            {
                Debug.LogWarning($"LayoutGroupAction on {gameObject.name} has no target LayoutGroup.", this);
                return;
            }

            if (Delay > 0)
                sequence.AppendInterval(Delay);
            
            ITween layoutTween = null;
        
            if (Property == LayoutProperty.Spacing && 
                (TargetLayout is HorizontalLayoutGroup || TargetLayout is VerticalLayoutGroup))
            {
                if (TargetLayout is HorizontalLayoutGroup hlg)
                    layoutTween = UTween.To(() => hlg.spacing, x => {
                        hlg.spacing = x;
                        LayoutRebuilder.ForceRebuildLayoutImmediate(hlg.GetComponent<RectTransform>());
                    }, TargetSpacing, Duration);
                else if (TargetLayout is VerticalLayoutGroup vlg)
                    layoutTween = UTween.To(() => vlg.spacing, x => {
                        vlg.spacing = x;
                        LayoutRebuilder.ForceRebuildLayoutImmediate(vlg.GetComponent<RectTransform>());
                    }, TargetSpacing, Duration);
            }
            else if (Property == LayoutProperty.Padding)
            {
                layoutTween = UTweenVirtual.Float(0, 1, Duration, t => {
                    int currentLeft = Mathf.RoundToInt(Mathf.Lerp(TargetLayout.padding.left, TargetPadding.left, t));
                    int currentRight = Mathf.RoundToInt(Mathf.Lerp(TargetLayout.padding.right, TargetPadding.right, t));
                    int currentTop = Mathf.RoundToInt(Mathf.Lerp(TargetLayout.padding.top, TargetPadding.top, t));
                    int currentBottom = Mathf.RoundToInt(Mathf.Lerp(TargetLayout.padding.bottom, TargetPadding.bottom, t));
                
                    TargetLayout.padding = new RectOffset(currentLeft, currentRight, currentTop, currentBottom);
                    LayoutRebuilder.ForceRebuildLayoutImmediate(TargetLayout.GetComponent<RectTransform>());
                });
            }
            
            if (layoutTween != null)
            {
                layoutTween.SetEase(EaseType);
                sequence.Append(layoutTween);
            }
        }
    }
}