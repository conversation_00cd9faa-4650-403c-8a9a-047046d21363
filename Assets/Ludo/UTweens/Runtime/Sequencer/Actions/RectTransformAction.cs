using Ludo.UTweens.Extensions;
using UnityEngine;

namespace Ludo.UTweens.Runtime.Sequencer
{
    public class RectTransformAction : TweenAction
    {
        public enum RectTransformProperty { AnchoredPosition, <PERSON>zeDel<PERSON>, Pivot }
        
        [Header("RectTransform Action Settings")]
        [Tooltip("The RectTransform to modify. If null, uses this GameObject's RectTransform.")]
        public RectTransform TargetRectTransform;
        
        [<PERSON>lt<PERSON>("Which property of the RectTransform to animate")]
        public RectTransformProperty Property = RectTransformProperty.AnchoredPosition;
        
        [Tooltip("Target Vector2 value")]
        public Vector2 TargetValue = Vector2.zero;
        
        [Tooltip("If true, only animate X component")]
        public bool OnlyX = false;
        
        [Tooltip("If true, only animate Y component")]
        public bool OnlyY = false;

        void Awake()
        {
            if (TargetRectTransform == null)
            {
                TargetRectTransform = GetComponent<RectTransform>();
            }
        }

        public override void AddToSequence(Sequence sequence)
        {
            if (TargetRectTransform == null)
            {
                Debug.LogWarning($"RectTransformAction on {gameObject.name} has no target RectTransform.", this);
                return;
            }

            if (Delay > 0)
            {
                sequence.AppendInterval(Delay);
            }

            ITween rectTween = null;
            
            // Handle each property type
            switch (Property)
            {
                case RectTransformProperty.AnchoredPosition:
                    if (OnlyX)
                    {
                        rectTween = TargetRectTransform.UAnchorPosX(TargetValue.x, Duration);
                    }
                    else if (OnlyY)
                    {
                        rectTween = TargetRectTransform.UAnchorPosY(TargetValue.y, Duration);
                    }
                    else
                    {
                        rectTween = TargetRectTransform.UAnchorPos(TargetValue, Duration);
                    }
                    break;
                    
                case RectTransformProperty.SizeDelta:
                    rectTween = TargetRectTransform.USizeDelta(TargetValue, Duration);
                    break;
                    
                case RectTransformProperty.Pivot:
                    rectTween = TargetRectTransform.UPivot(TargetValue, Duration);
                    break;
            }

            // Apply common settings
            rectTween.SetEase(EaseType);

            // Append to the main sequence
            sequence.Append(rectTween);
        }
    }
}