using System.Collections.Generic;
using UnityEngine;

namespace Ludo.UTweens.Runtime.Sequencer
{
    public class RandomAction : TweenAction
    {
        [<PERSON><PERSON>("Random Action Settings")]
        [Tooltip("List of possible actions to choose from")]
        public List<TweenAction> PossibleActions = new List<TweenAction>();
    
        [Tooltip("Weights for each action (leave empty for equal weights)")]
        public List<float> ActionWeights = new List<float>();
    
        [Tooltip("If true, will choose a new random action each loop")]
        public bool NewRandomEachLoop = false;
    
        [Tooltip("If true, will use the Duration field above, otherwise uses selected action's duration")]
        public bool UseFixedDuration = false;
    
        private int _selectedActionIndex = -1;

        public override void AddToSequence(Sequence sequence)
        {
            if (PossibleActions.Count == 0)
            {
                Debug.LogWarning($"RandomAction on {gameObject.name} has no possible actions.", this);
                return;
            }

            if (Delay > 0)
                sequence.AppendInterval(Delay);
            
            // Choose a random action if not already selected or if we need a new one per loop
            if (_selectedActionIndex < 0 || NewRandomEachLoop)
            {
                if (ActionWeights.Count == PossibleActions.Count)
                {
                    // Weighted random selection
                    float totalWeight = 0;
                    foreach (float weight in ActionWeights)
                        totalWeight += weight;
                    
                    float randomValue = UnityEngine.Random.Range(0f, totalWeight);
                    float accumulatedWeight = 0;
                
                    for (int i = 0; i < ActionWeights.Count; i++)
                    {
                        accumulatedWeight += ActionWeights[i];
                        if (randomValue <= accumulatedWeight)
                        {
                            _selectedActionIndex = i;
                            break;
                        }
                    }
                }
                else
                {
                    // Equal probability
                    _selectedActionIndex = UnityEngine.Random.Range(0, PossibleActions.Count);
                }
            }
        
            // Use the selected action
            if (_selectedActionIndex >= 0 && _selectedActionIndex < PossibleActions.Count)
            {
                TweenAction selectedAction = PossibleActions[_selectedActionIndex];
            
                if (selectedAction != null)
                {
                    // Override duration if needed
                    float originalDuration = selectedAction.Duration;
                    if (UseFixedDuration)
                        selectedAction.Duration = this.Duration;
                    
                    // Add to sequence
                    selectedAction.AddToSequence(sequence);
                
                    // Restore original duration
                    if (UseFixedDuration)
                        selectedAction.Duration = originalDuration;
                }
            }
        }
    }
}