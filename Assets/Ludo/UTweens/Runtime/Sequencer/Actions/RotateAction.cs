using Ludo.UTweens.Extensions;
using UnityEngine;

namespace Ludo.UTweens.Runtime.Sequencer
{
    public class RotateAction : TweenAction
    {
        [Header("Rotate Action Settings")]
        [Tooltip("The Transform to rotate. If null, uses this GameObject's Transform.")]
        public Transform TargetTransform;
        public Vector3 TargetRotation;
        public bool IsLocalRotation = false;
        public bool UseQuaternion = false;
        [Tooltip("Only used if UseQuaternion is true.")]
        public Quaternion TargetQuaternion;

        // Automatically assign target if null
        void Awake()
        {
            if (TargetTransform == null)
            {
                TargetTransform = transform;
            }
        }

        public override void AddToSequence(Sequence sequence)
        {
            if (TargetTransform == null && !TryGetComponent(out TargetTransform))
            {
                Debug.LogWarning($"RotateAction on {gameObject.name} has no target Transform.", this);
                return;
            }
            
            if (Delay > 0)
            {
                sequence.AppendInterval(Delay); 
            }

            // Create the specific tween
            ITween rotateTween;
            
            if (UseQuaternion)
            {
                rotateTween = IsLocalRotation
                    ? TargetTransform.URotateLocalQuaternion(TargetQuaternion, Duration)
                    : TargetTransform.URotateQuaternion(TargetQuaternion, Duration);
            }
            else
            {
                rotateTween = IsLocalRotation
                    ? TargetTransform.ULocalRotate(TargetRotation, Duration)
                    : TargetTransform.URotate(TargetRotation, Duration);
            }

            // Apply common settings
            rotateTween.SetEase(EaseType);

            // Append to the main sequence
            sequence.Append(rotateTween);
        }
    }
}