using Ludo.UTweens.Extensions;
using UnityEngine;
using UnityEngine.UI;

namespace Ludo.UTweens.Runtime.Sequencer
{
    public class SliderAction : TweenAction
    {
        [Head<PERSON>("Slider Settings")]
        [Tooltip("The Slider to modify. If null, uses this GameObject's Slider.")]
        public Slider TargetSlider;
        
        [Toolt<PERSON>("Target value for the slider")]
        public float TargetValue = 1.0f;
        
        [Toolt<PERSON>("If true, will round the values to integers")]
        public bool Snapping = false;
        
        [Head<PERSON>("Optional Settings")]
        [Tooltip("If true, will also set min and max values")]
        public bool SetMinMax = false;
        
        [Toolt<PERSON>("Minimum value to set")]
        public float MinValue = 0f;
        
        [Tooltip("Maximum value to set")]
        public float MaxValue = 1f;
        
        [Tooltip("If true, slider will be made interactive after the tween completes")]
        public bool MakeInteractable = false;

        void Awake()
        {
            if (TargetSlider == null)
            {
                TargetSlider = GetComponent<Slider>();
            }
        }

        public override void AddToSequence(Sequence sequence)
        {
            if (TargetSlider == null)
            {
                Debug.LogWarning($"SliderAction on {gameObject.name} has no target Slider.", this);
                return;
            }

            if (Delay > 0)
            {
                sequence.AppendInterval(Delay);
            }

            // Configure min/max values if requested
            if (SetMinMax)
            {
                sequence.AppendCallback(() => 
                {
                    TargetSlider.minValue = MinValue;
                    TargetSlider.maxValue = MaxValue;
                    
                    // Make sure the slider is non-interactive during the tween
                    if (MakeInteractable)
                    {
                        TargetSlider.interactable = false;
                    }
                });
            }

            // Create the slider value tween
            ITween sliderTween = TargetSlider.UValue(TargetValue, Duration, Snapping);

            // Apply common settings
            sliderTween.SetEase(EaseType);
            
            // Make the slider interactive after the tween if requested
            if (MakeInteractable)
            {
                sliderTween.OnComplete(_ => TargetSlider.interactable = true);
            }

            // Append to the main sequence
            sequence.Append(sliderTween);
        }
    }
    
}