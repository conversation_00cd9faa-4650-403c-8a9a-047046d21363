using Ludo.UTweens.Extensions;
using UnityEngine;
using UnityEngine.UI;

namespace Ludo.UTweens.Runtime.Sequencer
{
    public class ImageFillAction : TweenAction
    {
        [Header("Image Fill Settings")]
        [Tooltip("The Image to modify. If null, uses this GameObject's Image.")]
        public Image TargetImage;
        
        [Tooltip("Target fill amount (0-1)")]
        [Range(0f, 1f)]
        public float TargetFillAmount = 1.0f;
        
        [<PERSON><PERSON>("Optional Fill Settings")]
        [Tooltip("If true, will also set fill method at start of tween")]
        public bool SetFillMethod = false;
        
        [Tooltip("Fill method to use")]
        public Image.FillMethod FillMethod = Image.FillMethod.Radial360;
        
        [Tooltip("Fill origin to use")]
        public int FillOrigin = 0;
        
        [Tooltip("If true for radial fill, will fill clockwise")]
        public bool Clockwise = true;

        void Awake()
        {
            if (TargetImage == null)
            {
                TargetImage = GetComponent<Image>();
            }
        }

        public override void AddToSequence(Sequence sequence)
        {
            if (TargetImage == null)
            {
                Debug.LogWarning($"ImageFillAction on {gameObject.name} has no target Image.", this);
                return;
            }

            if (Delay > 0)
            {
                sequence.AppendInterval(Delay);
            }

            // Configure fill settings if requested
            if (SetFillMethod)
            {
                sequence.AppendCallback(() => 
                {
                    TargetImage.fillMethod = FillMethod;
                    TargetImage.fillOrigin = FillOrigin;
                    
                    if (FillMethod == Image.FillMethod.Radial90 || 
                        FillMethod == Image.FillMethod.Radial180 || 
                        FillMethod == Image.FillMethod.Radial360)
                    {
                        TargetImage.fillClockwise = Clockwise;
                    }
                });
            }

            // Create the fill amount tween
            ITween fillTween = TargetImage.UFillAmount(TargetFillAmount, Duration);

            // Apply common settings
            fillTween.SetEase(EaseType);

            // Append to the main sequence
            sequence.Append(fillTween);
        }
    }
}