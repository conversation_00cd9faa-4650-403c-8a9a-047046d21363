using UnityEngine;

namespace Ludo.UTweens.Runtime.Sequencer
{
    public class FollowAction : TweenAction
    {
        [<PERSON><PERSON>("Follow Settings")]
        [Tooltip("The Transform that will follow the target. If null, uses this GameObject's Transform.")]
        public Transform FollowerTransform;
    
        [<PERSON>lt<PERSON>("The Transform to follow. If null, will use TargetPosition instead.")]
        public Transform TargetTransform;
    
        [Toolt<PERSON>("Target position (used if TargetTransform is null)")]
        public Vector3 TargetPosition;
    
        [<PERSON>ltip("Damping factor (smoothness of movement, lower = smoother)")]
        [Range(0.1f, 20f)]
        public float DampingFactor = 5f;
    
        [Tooltip("Distance tolerance to consider 'arrived'")]
        public float DistanceTolerance = 0.1f;
    
        [Toolt<PERSON>("If true, will also match rotation")]
        public bool MatchRotation = false;
    
        [Tooltip("Rotation damping factor")]
        [Range(0.1f, 20f)]
        public float RotationDampingFactor = 5f;
    
        [<PERSON>lt<PERSON>("If true, uses local space instead of world space")]
        public bool UseLocalSpace = false;

        public override void AddToSequence(Sequence sequence)
        {
            if (FollowerTransform == null)
                FollowerTransform = transform;
            
            if (FollowerTransform == null)
            {
                Debug.LogWarning($"FollowAction on {gameObject.name} has no follower Transform.", this);
                return;
            }

            if (Delay > 0)
                sequence.AppendInterval(Delay);
            
            // Create a virtual tween since we need to update every frame
            ITween followTween = UTweenVirtual.Float(0, 1, Duration, t => {
                Vector3 targetPos = TargetTransform != null ? TargetTransform.position : TargetPosition;
                Quaternion targetRot = TargetTransform != null ? TargetTransform.rotation : FollowerTransform.rotation;
            
                if (UseLocalSpace && FollowerTransform.parent != null)
                {
                    targetPos = FollowerTransform.parent.InverseTransformPoint(targetPos);
                    if (TargetTransform != null)
                        targetRot = Quaternion.Inverse(FollowerTransform.parent.rotation) * targetRot;
                }
            
                // Calculate position based on smoothed lerp
                float easedT = EasingFunctions.Evaluate(EaseType, t);
                float speed = DampingFactor * Time.deltaTime;
            
                if (UseLocalSpace)
                {
                    FollowerTransform.localPosition = Vector3.Lerp(
                        FollowerTransform.localPosition, 
                        targetPos, 
                        speed * (1 + 9 * easedT) // Gradually increase speed
                    );
                
                    if (MatchRotation)
                    {
                        FollowerTransform.localRotation = Quaternion.Lerp(
                            FollowerTransform.localRotation,
                            targetRot,
                            RotationDampingFactor * Time.deltaTime * (1 + 9 * easedT)
                        );
                    }
                }
                else
                {
                    FollowerTransform.position = Vector3.Lerp(
                        FollowerTransform.position, 
                        targetPos, 
                        speed * (1 + 9 * easedT)
                    );
                
                    if (MatchRotation)
                    {
                        FollowerTransform.rotation = Quaternion.Lerp(
                            FollowerTransform.rotation,
                            targetRot,
                            RotationDampingFactor * Time.deltaTime * (1 + 9 * easedT)
                        );
                    }
                }
            });
            
            sequence.Append(followTween);
        }
    }
}