using UnityEngine;
using UnityEngine.UI;

namespace Ludo.UTweens.Runtime.Sequencer
{
    public class InputFieldAction : TweenAction
    {
        [<PERSON><PERSON>("Input Field Settings")]
        [Tooltip("The InputField to modify. If null, uses this GameObject's InputField.")]
        public InputField TargetInputField;
    
        [Tooltip("Target text to set")]
        public string TargetText = "";
    
        [Toolt<PERSON>("Animation type")]
        public enum TextAnimationType { Instant, TypeWriter, Fade }
    
        [Tooltip("How to animate the text change")]
        public TextAnimationType AnimationType = TextAnimationType.TypeWriter;
    
        [Tooltip("If true, will clear the field first")]
        public bool ClearFirst = true;
    
        [Tooltip("If true, will toggle interactability during animation")]
        public bool DisableDuringAnimation = true;
    
        [Tooltip("If true, will show placeholder during typing")]
        public bool ShowPlaceholder = false;

        public override void AddToSequence(Sequence sequence)
        {
            if (TargetInputField == null)
                TargetInputField = GetComponent<InputField>();
            
            if (TargetInputField == null)
            {
                Debug.LogWarning($"InputFieldAction on {gameObject.name} has no target InputField.", this);
                return;
            }

            if (Delay > 0)
                sequence.AppendInterval(Delay);
        
            // Store original state
            bool wasInteractable = TargetInputField.interactable;
            string originalText = TargetInputField.text;
            string originalPlaceholder = "";
            if (TargetInputField.placeholder != null && TargetInputField.placeholder is Text placeholderText)
                originalPlaceholder = placeholderText.text;
            
            ITween inputTween = null;
        
            switch (AnimationType)
            {
                case TextAnimationType.Instant:
                    inputTween = UTweenVirtual.Float(0, 1, Duration, t => {
                        if (t >= 0.5f && TargetInputField.text != TargetText)
                            TargetInputField.text = TargetText;
                    });
                    break;
                
                case TextAnimationType.TypeWriter:
                    inputTween = UTweenVirtual.Float(0, 1, Duration, t => {
                        if (t <= 0.05f && ClearFirst)
                            TargetInputField.text = "";
                        
                        int charCount = Mathf.FloorToInt(t * TargetText.Length);
                        TargetInputField.text = TargetText.Substring(0, charCount);
                    });
                    break;
                
                case TextAnimationType.Fade:
                    // For fade, we need to use a sequence since we can't directly fade the text
                    Sequence fadeSequence = UTween.Sequence();
                
                    // First fade out
                    fadeSequence.Append(UTweenVirtual.Float(1, 0, Duration * 0.4f, fadeOut => {
                        // Fade out using alpha on input field
                        if (TargetInputField.textComponent != null)
                            TargetInputField.textComponent.color = new Color(
                                TargetInputField.textComponent.color.r,
                                TargetInputField.textComponent.color.g,
                                TargetInputField.textComponent.color.b,
                                fadeOut
                            );
                    }));
                
                    // Change text
                    fadeSequence.AppendCallback(() => {
                        TargetInputField.text = TargetText;
                    });
                
                    // Then fade in
                    fadeSequence.Append(UTweenVirtual.Float(0, 1, Duration * 0.4f, fadeIn => {
                        // Fade in using alpha on input field
                        if (TargetInputField.textComponent != null)
                            TargetInputField.textComponent.color = new Color(
                                TargetInputField.textComponent.color.r,
                                TargetInputField.textComponent.color.g,
                                TargetInputField.textComponent.color.b,
                                fadeIn
                            );
                    }));
                
                    inputTween = fadeSequence;
                    break;
            }
        
            // Set up interactability toggle
            if (DisableDuringAnimation)
            {
                inputTween.OnStart(_ => {
                    TargetInputField.interactable = false;
                });
            
                inputTween.OnComplete(_ => {
                    TargetInputField.interactable = wasInteractable;
                });
            }
        
            // Handle placeholder
            if (ShowPlaceholder && AnimationType == TextAnimationType.TypeWriter)
            {
                inputTween.OnStart(_ => {
                    if (TargetInputField.placeholder != null && TargetInputField.placeholder is Text placeholderText)
                        placeholderText.text = TargetText;
                });
            
                inputTween.OnComplete(_ => {
                    if (TargetInputField.placeholder != null && TargetInputField.placeholder is Text placeholderText)
                        placeholderText.text = originalPlaceholder;
                });
            }
            
            if (inputTween != null)
            {
                inputTween.SetEase(EaseType);
                sequence.Append(inputTween);
            }
        }
    }
}