using System.Collections.Generic;
using Ludo.UTweens.Extensions;
using UnityEngine;

namespace Ludo.UTweens.Runtime.Sequencer
{
    public class PathFollowAction : TweenAction
    {
        [Header("Path Follow Settings")]
        [Tooltip("The Transform to move along the path. If null, uses this GameObject's Transform.")]
        public Transform TargetTransform;
        
        [Tooltip("Waypoints that define the path")]
        public List<Transform> Waypoints = new List<Transform>();

        [Toolt<PERSON>("If no transforms are in Waypoints, these positions will be used instead")]
        public List<Vector3> WaypointPositions = new List<Vector3>();

        [Tooltip("If true, object will orient to face the direction of movement")]
        public bool OrientToPath = false;

        [Tooltip("If true, will use local space for the transforms")]
        public bool UseLocalSpace = false;

        [Tooltip("If true, closes the path by connecting the last point to the first point")]
        public bool ClosePath = false;

        private List<Vector3> _pathPoints = new List<Vector3>();

        void Awake()
        {
            if (TargetTransform == null)
            {
                TargetTransform = transform;
            }
        }

        public override void AddToSequence(Sequence sequence)
        {
            if (TargetTransform == null)
            {
                Debug.LogWarning($"PathFollowAction on {gameObject.name} has no target Transform.", this);
                return;
            }

            // Build path points
            _pathPoints.Clear();
            if (Waypoints.Count > 0)
            {
                foreach (Transform waypoint in Waypoints)
                {
                    if (waypoint != null)
                    {
                        _pathPoints.Add(UseLocalSpace ? waypoint.localPosition : waypoint.position);
                    }
                }
            }
            else if (WaypointPositions.Count > 0)
            {
                _pathPoints.AddRange(WaypointPositions);
            }
            
            if (_pathPoints.Count < 2)
            {
                Debug.LogWarning($"PathFollowAction on {gameObject.name} needs at least 2 waypoints.", this);
                return;
            }

            if (Delay > 0)
            {
                sequence.AppendInterval(Delay);
            }

            // Create a sequence for the path following
            Sequence pathSequence = UTween.Sequence();

            // Starting position
            Vector3 startPos = UseLocalSpace ? TargetTransform.localPosition : TargetTransform.position;
            
            // Add first segment (from current position to first waypoint)
            if (UseLocalSpace)
            {
                pathSequence.Append(TargetTransform.ULocalMove(_pathPoints[0], Duration / _pathPoints.Count));
            }
            else
            {
                pathSequence.Append(TargetTransform.UMove(_pathPoints[0], Duration / _pathPoints.Count));
            }

            // Add remaining segments
            for (int i = 1; i < _pathPoints.Count; i++)
            {
                Vector3 currentTarget = _pathPoints[i];
                
                // Create movement tween
                ITween moveTween;
                if (UseLocalSpace)
                {
                    moveTween = TargetTransform.ULocalMove(currentTarget, Duration / _pathPoints.Count);
                }
                else
                {
                    moveTween = TargetTransform.UMove(currentTarget, Duration / _pathPoints.Count);
                }

                // Add rotation if needed
                if (OrientToPath && i > 0)
                {
                    Vector3 previousPos = _pathPoints[i - 1];
                    Vector3 direction = (currentTarget - previousPos).normalized;
                    
                    if (direction != Vector3.zero)
                    {
                        Quaternion targetRotation = Quaternion.LookRotation(direction);
                        
                        // Add rotation tween in parallel with movement
                        if (UseLocalSpace)
                        {
                            pathSequence.Join(TargetTransform.URotateLocalQuaternion(targetRotation, Duration / _pathPoints.Count));
                        }
                        else
                        {
                            pathSequence.Join(TargetTransform.URotateQuaternion(targetRotation, Duration / _pathPoints.Count));
                        }
                    }
                }

                pathSequence.Append(moveTween);
            }

            // Add a final segment to close the path if requested
            if (ClosePath && _pathPoints.Count > 2)
            {
                if (UseLocalSpace)
                {
                    pathSequence.Append(TargetTransform.ULocalMove(_pathPoints[0], Duration / _pathPoints.Count));
                }
                else
                {
                    pathSequence.Append(TargetTransform.UMove(_pathPoints[0], Duration / _pathPoints.Count));
                }
            }

            // Apply common settings
            pathSequence.SetEase(EaseType);

            // Append the entire path sequence to the main sequence
            sequence.Append(pathSequence);
        }
    }
}