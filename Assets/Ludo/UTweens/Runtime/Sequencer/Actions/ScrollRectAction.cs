using UnityEngine;
using UnityEngine.UI;

namespace Ludo.UTweens.Runtime.Sequencer
{
    public class ScrollRectAction : TweenAction
    {
        [Header("ScrollRect Settings")]
        [Tooltip("The ScrollRect to animate. If null, uses this GameObject's ScrollRect.")]
        public ScrollRect TargetScrollRect;
    
        [Tooltip("Target normalized position (0-1)")]
        [Range(0, 1)]
        public Vector2 TargetScrollPosition = Vector2.zero;
    
        [Tooltip("If true, only animate horizontal scrolling")]
        public bool HorizontalOnly = false;
    
        [Tooltip("If true, only animate vertical scrolling")]
        public bool VerticalOnly = false;

        public override void AddToSequence(Sequence sequence)
        {
            if (TargetScrollRect == null)
                TargetScrollRect = GetComponent<ScrollRect>();
            
            if (TargetScrollRect == null)
            {
                Debug.LogWarning($"ScrollRectAction on {gameObject.name} has no target ScrollRect.", this);
                return;
            }

            if (Delay > 0)
                sequence.AppendInterval(Delay);
            
            ITween scrollTween;
        
            if (HorizontalOnly)
                scrollTween = UTween.To(() => TargetScrollRect.horizontalNormalizedPosition, 
                    x => TargetScrollRect.horizontalNormalizedPosition = x, 
                    TargetScrollPosition.x, Duration);
            else if (VerticalOnly)
                scrollTween = UTween.To(() => TargetScrollRect.verticalNormalizedPosition, 
                    x => TargetScrollRect.verticalNormalizedPosition = x, 
                    TargetScrollPosition.y, Duration);
            else
                scrollTween = UTweenVirtual.Float(0, 1, Duration, t => {
                    TargetScrollRect.horizontalNormalizedPosition = Mathf.Lerp(
                        TargetScrollRect.horizontalNormalizedPosition, 
                        TargetScrollPosition.x, t);
                    TargetScrollRect.verticalNormalizedPosition = Mathf.Lerp(
                        TargetScrollRect.verticalNormalizedPosition, 
                        TargetScrollPosition.y, t);
                });
            
            scrollTween.SetEase(EaseType);
            sequence.Append(scrollTween);
        }
    }
}