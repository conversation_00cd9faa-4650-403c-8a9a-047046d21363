using UnityEngine;

namespace Ludo.UTweens.Runtime.Sequencer
{
    public class ParticleSystemAction : TweenAction
    {
        public enum ParticleProperty { EmissionRate, StartSize, StartSpeed, StartColor }
    
        [Header("Particle System Settings")]
        [Tooltip("The ParticleSystem to modify. If null, uses this GameObject's ParticleSystem.")]
        public ParticleSystem TargetParticleSystem;
    
        [Tooltip("Property to animate")]
        public ParticleProperty Property = ParticleProperty.EmissionRate;
    
        [Tooltip("Target emission rate")]
        public float TargetEmissionRate = 10f;
    
        [Tooltip("Target start size")]
        public float TargetStartSize = 1f;
    
        [Tooltip("Target start speed")]
        public float TargetStartSpeed = 5f;
    
        [Tooltip("Target start color")]
        public Color TargetStartColor = Color.white;
    
        [Header("Control Settings")]
        [Tooltip("If true, will play the particle system at start")]
        public bool PlayOnStart = false;
    
        [Tooltip("If true, will stop the particle system at end")]
        public bool StopOnEnd = false;

        public override void AddToSequence(Sequence sequence)
        {
            if (TargetParticleSystem == null)
                TargetParticleSystem = GetComponent<ParticleSystem>();
            
            if (TargetParticleSystem == null)
            {
                Debug.LogWarning($"ParticleSystemAction on {gameObject.name} has no target ParticleSystem.", this);
                return;
            }

            if (Delay > 0)
                sequence.AppendInterval(Delay);
            
            // Store initial values
            var emission = TargetParticleSystem.emission;
            var main = TargetParticleSystem.main;
        
            float initialEmissionRate = emission.rateOverTime.constant;
            float initialSize = main.startSize.constant;
            float initialSpeed = main.startSpeed.constant;
            Color initialColor = main.startColor.color;
            
            ITween particleTween = UTweenVirtual.Float(0, 1, Duration, t => {
                switch (Property)
                {
                    case ParticleProperty.EmissionRate:
                        var emissionModule = TargetParticleSystem.emission;
                        var rate = emissionModule.rateOverTime;
                        rate.constant = Mathf.Lerp(initialEmissionRate, TargetEmissionRate, t);
                        emissionModule.rateOverTime = rate;
                        break;
                    
                    case ParticleProperty.StartSize:
                        var mainModule = TargetParticleSystem.main;
                        var size = mainModule.startSize;
                        size.constant = Mathf.Lerp(initialSize, TargetStartSize, t);
                        mainModule.startSize = size;
                        break;
                    
                    case ParticleProperty.StartSpeed:
                        var mainModule2 = TargetParticleSystem.main;
                        var speed = mainModule2.startSpeed;
                        speed.constant = Mathf.Lerp(initialSpeed, TargetStartSpeed, t);
                        mainModule2.startSpeed = speed;
                        break;
                    
                    case ParticleProperty.StartColor:
                        var mainModule3 = TargetParticleSystem.main;
                        var color = mainModule3.startColor;
                        color.color = Color.Lerp(initialColor, TargetStartColor, t);
                        mainModule3.startColor = color;
                        break;
                }
            });
        
            if (PlayOnStart)
            {
                particleTween.OnStart(_ => {
                    if (!TargetParticleSystem.isPlaying)
                        TargetParticleSystem.Play();
                });
            }
        
            if (StopOnEnd)
            {
                particleTween.OnComplete(_ => {
                    if (TargetParticleSystem.isPlaying)
                        TargetParticleSystem.Stop();
                });
            }
            
            particleTween.SetEase(EaseType);
            sequence.Append(particleTween);
        }
    }
}