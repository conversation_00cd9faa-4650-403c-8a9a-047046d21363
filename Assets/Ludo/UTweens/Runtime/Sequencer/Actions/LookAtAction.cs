using Ludo.UTweens.Extensions;
using Ludo.UTweens.Plugins;
using UnityEngine;

namespace Ludo.UTweens.Runtime.Sequencer
{
    public class LookAtAction : TweenAction
    {
        [Header("Look At Settings")]
        [Tooltip("The Transform that will look at the target. If null, uses this GameObject's Transform.")]
        public Transform SourceTransform;
        
        [<PERSON>lt<PERSON>("The Transform to look at. If null, will use TargetPosition instead.")]
        public Transform TargetTransform;
        
        [<PERSON><PERSON><PERSON>("Position to look at (used if TargetTransform is null)")]
        public Vector3 TargetPosition;
        
        [Tooltip("Up direction for the look rotation")]
        public Vector3 UpDirection = Vector3.up;
        
        [Tooltip("Constrain rotation to specific axes")]
        public AxisConstraint AxisConstraint = AxisConstraint.None;
        
        [Tooltip("If true, will use world-space up direction; if false will use local-space up direction")]
        public bool WorldUpDirection = true;
        
        [Tooltip("If true, will update the target position every frame (if TargetTransform is assigned)")]
        public bool TrackMovingTarget = false;

        void Awake()
        {
            if (SourceTransform == null)
            {
                SourceTransform = transform;
            }
        }

        public override void AddToSequence(Sequence sequence)
        {
            if (SourceTransform == null)
            {
                Debug.LogWarning($"LookAtAction on {gameObject.name} has no source Transform.", this);
                return;
            }

            if (Delay > 0)
            {
                sequence.AppendInterval(Delay);
            }

            // Calculate target position
            Vector3 lookTarget = TargetTransform != null ? TargetTransform.position : TargetPosition;
            
            if (TrackMovingTarget && TargetTransform != null)
            {
                // For a moving target, we'll need to create a virtual tween
                ITween lookTween = UTweenVirtual.Float(0, 1, Duration, t =>
                {
                    // Get current target position
                    Vector3 currentTarget = TargetTransform.position;
                    
                    // Calculate rotation to look at the target
                    Quaternion currentRotation = SourceTransform.rotation;
                    Quaternion targetRotation = Quaternion.LookRotation(
                        currentTarget - SourceTransform.position, 
                        WorldUpDirection ? UpDirection : SourceTransform.TransformDirection(UpDirection)
                    );
                    
                    // Apply eased interpolation
                    Quaternion newRotation = Quaternion.Slerp(currentRotation, targetRotation, t);
                    
                    // Apply axis constraints
                    if (AxisConstraint != AxisConstraint.None)
                    {
                        Vector3 eulerAngles = newRotation.eulerAngles;
                        Vector3 currentEuler = currentRotation.eulerAngles;
                        
                        if ((AxisConstraint & AxisConstraint.X) == 0)
                            eulerAngles.x = currentEuler.x;
                        if ((AxisConstraint & AxisConstraint.Y) == 0)
                            eulerAngles.y = currentEuler.y;
                        if ((AxisConstraint & AxisConstraint.Z) == 0)
                            eulerAngles.z = currentEuler.z;
                            
                        newRotation = Quaternion.Euler(eulerAngles);
                    }
                    
                    // Apply rotation
                    SourceTransform.rotation = newRotation;
                });
                
                // Apply common settings
                lookTween.SetEase(EaseType);
                
                // Append to the main sequence
                sequence.Append(lookTween);
            }
            else
            {
                // For a static target, we can use the standard LookAt tween
                Vector3 upDir = WorldUpDirection ? UpDirection : SourceTransform.TransformDirection(UpDirection);
                ITween lookTween = SourceTransform.ULookAt(lookTarget - SourceTransform.position, Duration, AxisConstraint, upDir);
                
                // Apply common settings
                lookTween.SetEase(EaseType);
                
                // Append to the main sequence
                sequence.Append(lookTween);
            }
        }
    }
}