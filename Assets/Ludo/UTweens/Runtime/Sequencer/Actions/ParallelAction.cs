using System.Collections.Generic;
using UnityEngine;

namespace Ludo.UTweens.Runtime.Sequencer
{
    /// <summary>
    /// A container that allows multiple tween actions to run in parallel within a sequence.
    /// </summary>
    public class ParallelAction : TweenAction
    {
        [Header("Parallel Actions Settings")]
        [Tooltip("Actions that will execute in parallel.")]
        public List<TweenAction> ParallelActions = new List<TweenAction>();
        
        [Tooltip("If true, will use the longest duration among child actions. If false, uses the Duration field above.")]
        public bool UseMaxDuration = true;

        public override void AddToSequence(Sequence sequence)
        {
            if (ParallelActions.Count == 0)
            {
                Debug.LogWarning($"ParallelAction on {gameObject.name} has no child actions.", this);
                return;
            }

            // Handle delay for the entire parallel group
            if (Delay > 0)
            {
                sequence.AppendInterval(Delay);
            }

            // Create a local sequence for this parallel group
            Sequence parallelSequence = UTween.Sequence();
            
            // First action is appended normally
            TweenAction firstAction = ParallelActions[0];
            if (firstAction != null)
            {
                firstAction.AddToSequence(parallelSequence);
            }
            else
            {
                Debug.LogWarning($"The first parallel action in {gameObject.name} is null.", this);
            }

            // Remaining actions are joined (run in parallel)
            for (int i = 1; i < ParallelActions.Count; i++)
            {
                TweenAction action = ParallelActions[i];
                if (action != null)
                {
                    // Create a separate sequence for this parallel action
                    Sequence actionSequence = UTween.Sequence();
                    action.AddToSequence(actionSequence);
                    
                    // Join with the main parallel sequence
                    parallelSequence.Join(actionSequence);
                }
            }

            // If not using max duration, enforce the specified duration
            if (!UseMaxDuration && Duration > 0)
            {
                parallelSequence.SetDelay(0); // Clear any internal delays
                
                // Create a dummy tween to enforce total duration
                ITween dummyTween = UTweenVirtual.Float(0, 1, Duration, _ => {});
                dummyTween.SetEase(EaseType);
                
                // We'll use a new sequence to wrap everything with the correct duration
                Sequence wrappedSequence = UTween.Sequence();
                wrappedSequence.Append(parallelSequence);
                wrappedSequence.Join(dummyTween);
                
                // Add the wrapped sequence to the main sequence
                sequence.Append(wrappedSequence);
            }
            else
            {
                // Just add the parallel sequence with its natural duration
                parallelSequence.SetEase(EaseType);
                sequence.Append(parallelSequence);
            }
        }

        private void OnValidate()
        {
            if (UseMaxDuration)
            {
                // Calculate the max duration among all child actions
                float maxDuration = 0f;
                foreach (var action in ParallelActions)
                {
                    if (action != null)
                    {
                        float actionTotalDuration = action.Duration + action.Delay;
                        if (actionTotalDuration > maxDuration)
                        {
                            maxDuration = actionTotalDuration;
                        }
                    }
                }
                Duration = maxDuration;
            }
        }
    }
}