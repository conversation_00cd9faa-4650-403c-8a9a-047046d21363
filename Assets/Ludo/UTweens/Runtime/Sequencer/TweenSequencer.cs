using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Events;

namespace Ludo.UTweens.Runtime.Sequencer
{
    public class TweenSequencer : TweenAction 
    {
        [Header("Sequence Events")]
        public UnityEvent OnSequenceStart;
        public UnityEvent OnSequenceComplete;
        public UnityEvent OnSequenceLoop;
        public UnityEvent OnSequencePause;
        public UnityEvent OnSequenceResume;
        
        [Header("Nested Sequence Settings")]
        [Tooltip("Action components defining this nested sequence. Order matters!")]
        public List<TweenAction> Actions = new List<TweenAction>();

        // Options for the sequence itself (Loops, InitialDelay for *this* sequence)
        [Header("Sequence Playback Settings")] 
        public int Loops = 0; // How many times this nested sequence should loop
        public LoopType LoopType = LoopType.Restart;
        public bool AutoPlayOnStart = false; // If this sequencer is top-level
        public float SequenceStartDelay = 0.0f; // Delay before this nested sequence starts *its* playback

        // Note: The Delay, Duration, EaseType from the base TweenAction class
        // now apply to how this *entire nested sequence* is treated when added
        // to an *outer* sequence.

        private Sequence _activeSequence;

        void Start()
        {
            CalcDuration(); // Calculate the total duration of this sequence based on its actions
            // AutoPlay only makes sense if this is a top-level sequencer (not nested)
            // You might need better logic to determine this if necessary.
            if (AutoPlayOnStart)
            {
                PlaySequence();
            }
        }

        public Sequence PlaySequence()
        {
            if (_activeSequence != null && _activeSequence.Active)
            {
                _activeSequence.Kill();
            }

            _activeSequence = BuildSequence();
            if (_activeSequence != null)
            {
                // Register sequence events
                _activeSequence.OnStart((_) => OnSequenceStart?.Invoke());
                _activeSequence.OnComplete((_) => OnSequenceComplete?.Invoke());
                _activeSequence.OnStepComplete((_) => {
                    if (Loops > 0) OnSequenceLoop?.Invoke();
                });
        
                _activeSequence.Play();
            }

            return _activeSequence;
        }

        // BuildSequence remains largely the same - it builds the *inner* sequence
        public Sequence BuildSequence()
        {
            if (!UTween.Initialized) UTween.Init(); // [cite: uploaded:UTweens/Runtime/UTween.cs]

            Sequence
                innerSequence =
                    UTween.Sequence(); // [cite: uploaded:UTweens/Runtime/UTween.cs, uploaded:UTweens/Runtime/Sequence.cs]
            innerSequence.SetLoops(Loops, LoopType)
                .SetDelay(SequenceStartDelay) // Use the specific delay for this sequence's start
                .SetTarget(gameObject);

            foreach (var action in Actions)
            {
                if (action != null)
                {
                    // Recursively call AddToSequence for nested actions/sequences
                    action.AddToSequence(innerSequence);
                }
                else
                {
                    Debug.LogWarning($"TweenSequencer on {gameObject.name} has a null action in its list.", this);
                }
            }

            return innerSequence;
        }

        // --- Implementation of TweenAction ---

        public void AddCallback(float normalizedTime, UnityAction callback, bool triggerOnce = false)
        {
            if (_activeSequence != null)
            {
                // Add a callback at the specified position in the sequence
                _activeSequence.InsertCallback(normalizedTime * _activeSequence.Duration, () => {
                    callback.Invoke();
                });
            }
        }
        
        /// <summary>
        /// Adds this entire nested sequence as an item to an outer sequence.
        /// </summary>
        /// <param name="outerSequence">The sequence to add this nested sequence to.</param>
        public override void AddToSequence(Sequence outerSequence)
        {
            // Build the inner sequence defined by this component
            Sequence innerSequence = BuildSequence();

            if (innerSequence == null)
            {
                Debug.LogWarning($"Nested TweenSequencer on {gameObject.name} failed to build its inner sequence.",
                    this);
                return;
            }

            // Apply settings from the base TweenAction class (Delay, Duration, Ease)
            // to the *inner sequence itself* before adding it.

            // Apply the delay specified for this action step
            if (this.Delay > 0) // Using 'this.Delay' to refer to the base class field
            {
                outerSequence.AppendInterval(this.Delay);
            }


            // Append the fully built inner sequence to the outer one
            outerSequence.Append(innerSequence);
        }

        public void StopSequence()
        {
            if (_activeSequence != null && _activeSequence.Active)
            {
                _activeSequence.Kill();
                _activeSequence = null;
            }
        }

        private void CalcDuration()
        {
            // Calculate the total duration of this sequence based on its actions
            float totalDuration = 0f;
            foreach (var action in Actions)
            {
                if (action != null)
                {
                    totalDuration += action.Duration + action.Delay;
                }
            }

            // Set the duration of the sequence to the calculated total
            Duration = totalDuration;
        }
        
        #if UNITY_EDITOR
        private void OnValidate()
        {
            // Ensure the Actions list is not empty
            if (Actions.Count == 0)
            {
                return;
            }
            
            CalcDuration(); // Recalculate duration when validating in the editor
        }
        
        private void OnDrawGizmos()
        {
            CalcDuration();
        }
        
        #endif
        
    }
}