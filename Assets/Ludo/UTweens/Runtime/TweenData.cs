using System;
using UnityEngine;

namespace Ludo.UTweens
{
    /// <summary>
    /// Base class for all tween data
    /// </summary>
    public class TweenData
    {
        // Core tween settings
        public float Duration;
        public float Delay;
        public float ElapsedTime;
        public int Loops;
        public int CompletedLoops;
        public LoopType LoopType;
        public bool AutoKill;
        public bool Recyclable;
        public bool TimeScaleIndependent;
        public UpdateType UpdateType;
        public object Target;
        public object Id;
        public bool Active;
        public bool Paused;
        public bool IsBackwards;
        
        // Easing
        public Ease EaseType;
        public AnimationCurve EaseCurve;
        public bool UseCustomCurve;
        
        // Callbacks
        public Action<ITween> OnStart;
        public Action<ITween> OnPlay;
        public Action<ITween> OnPause;
        public Action<ITween> OnUpdate;
        public Action<ITween> OnStepComplete;
        public Action<ITween> OnComplete;
        public Action<ITween> OnKill;
        public Action<ITween> OnRewind;
        public Action<ITween, object, object> OnIdChanged;
        
        public float TimeScale { get; set; } = 1f;
        
        public void Init(float duration)
        {
            Duration = duration;
            Delay = 0;
            ElapsedTime = 0;
            Loops = 0;
            CompletedLoops = 0;
            LoopType = LoopType.Restart;
            AutoKill = UTween.DefaultSettings.DefaultAutoKill;
            Recyclable = UTween.DefaultSettings.DefaultRecyclable;
            TimeScaleIndependent = UTween.DefaultSettings.DefaultTimeScaleIndependent;
            UpdateType = UTween.DefaultSettings.DefaultUpdateType;
            Target = null;
            Id = null;
            Active = true;
            Paused = false;
            IsBackwards = false;
            EaseType = UTween.DefaultSettings.DefaultEase;
            EaseCurve = null;
            UseCustomCurve = false;
            
            OnStart = null;
            OnPlay = null;
            OnPause = null;
            OnUpdate = null;
            OnStepComplete = null;
            OnComplete = null;
            OnKill = null;
            OnRewind = null;
            OnIdChanged = null;
            
            TimeScale = 1f;
        }
        
        public void Reset()
        {
            // Clear all references
            Target = null;
            Id = null;
            EaseCurve = null;
            
            OnStart = null;
            OnPlay = null;
            OnPause = null;
            OnUpdate = null;
            OnStepComplete = null;
            OnComplete = null;
            OnKill = null;
            OnRewind = null;
            OnIdChanged = null;
        }
        
        public float GetEasedProgress(float linearProgress)
        {
            if (UseCustomCurve && EaseCurve != null)
            {
                return EaseCurve.Evaluate(linearProgress);
            }
    
            if (EaseType == Ease.CustomCurve)
            {
                // Fallback to Linear ease if custom curve is misconfigured
                return linearProgress;
            }
    
            return EasingFunctions.Evaluate(EaseType, linearProgress);
        }
        
        
    }
}