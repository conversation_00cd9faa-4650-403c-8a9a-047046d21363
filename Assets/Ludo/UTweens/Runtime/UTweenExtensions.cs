using System;
using System.Collections.Generic;
using Ludo.UTweens.Plugins;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace Ludo.UTweens.Extensions
{
    /// <summary>
    /// Extension methods for common Unity objects to easily create tweens
    /// </summary>
    public static class UTweenExtensions
    {
        public static Tween<float> SetSnapping(this Tween<float> tween, bool snapping)
        {
            return tween.SetOptions(snapping);
        }
    
        public static Tween<Vector3> SetAxisConstraint(this Tween<Vector3> tween, AxisConstraint constraint)
        {
            return tween.SetOptions(new Vector3Plugin.Vector3PluginOptions(tween.Snapping, constraint));
        }

        
        #region Transform Extensions

        /// <summary>
        /// Tweens the transform's position to the target value
        /// </summary>
        public static ITween UMove(this Transform target, Vector3 endValue, float duration)
        {
            return UTween.To(() => target.position, x => target.position = x, endValue, duration)
                .SetTarget(target);
        }

        /// <summary>
        /// Tweens the transform's local position to the target value
        /// </summary>
        public static ITween UMoveLocal(this Transform target, Vector3 endValue, float duration)
        {
            return UTween.To(() => target.localPosition, x => target.localPosition = x, endValue, duration)
                .SetTarget(target);
        }

        /// <summary>
        /// Tweens the transform's rotation to the target value (in euler angles)
        /// </summary>
        public static ITween URotate(this Transform target, Vector3 endValue, float duration)
        {
            Quaternion currentRotation = target.rotation;

            return UTween.To(
                () => target.rotation.eulerAngles,
                x => target.rotation = Quaternion.Euler(x),
                endValue,
                duration
            ).SetTarget(target);
        }

        /// <summary>
        /// Tweens the transform's local rotation to the target value (in euler angles)
        /// </summary>
        public static ITween ULocalRotate(this Transform target, Vector3 endValue, float duration)
        {
            return UTween.To(
                () => target.localRotation.eulerAngles,
                x => target.localRotation = Quaternion.Euler(x),
                endValue,
                duration
            ).SetTarget(target);
        }

        /// <summary>
        /// Tweens the transform's rotation using quaternion values
        /// </summary>
        public static ITween URotateQuaternion(this Transform target, Quaternion endValue, float duration)
        {
            return UTween.To(() => target.rotation, x => target.rotation = x, endValue, duration)
                .SetTarget(target);
        }

        /// <summary>
        /// Tweens the transform's local rotation using quaternion values
        /// </summary>
        public static ITween URotateLocalQuaternion(this Transform target, Quaternion endValue, float duration)
        {
            return UTween.To(() => target.localRotation, x => target.localRotation = x, endValue, duration)
                .SetTarget(target);
        }

        public static ITween ULookAt(this Transform target, Vector3 endValue, float duration,
            AxisConstraint axisConstraint = AxisConstraint.None, Vector3 up = default)
        {
            return UTween.To(() => target.rotation, x => target.rotation = x, Quaternion.LookRotation(endValue, up),
                    duration)
                .SetOptions(axisConstraint)
                .SetTarget(target);
        }

        public static ITween UMove(this Transform target, Vector3 endValue, float duration,
            AxisConstraint axisConstraint = AxisConstraint.None, bool snapping = false)
        {
            return UTween.To(() => target.position, x => target.position = x, endValue, duration)
                .SetOptions(axisConstraint)
                .SetOptions(snapping)
                .SetTarget(target);
        }

        public static ITween ULocalMove(this Transform target, Vector3 endValue, float duration,
            AxisConstraint axisConstraint = AxisConstraint.None, bool snapping = false)
        {
            return UTween.To(() => target.localPosition, x => target.localPosition = x, endValue, duration)
                .SetOptions(axisConstraint)
                .SetOptions(snapping)
                .SetTarget(target);
        }

        /// <summary>
        /// Tweens the transform's local scale to the target value
        /// </summary>
        public static ITween UScale(this Transform target, Vector3 endValue, float duration,
            AxisConstraint axisConstraint = AxisConstraint.None, bool snapping = false)
        {
            return UTween.To(() => target.localScale, x => target.localScale = x, endValue, duration)
                .SetOptions(new Vector3Plugin.Vector3PluginOptions(snapping, axisConstraint))
                .SetTarget(target);
        }
        
        


        /// <summary>
        /// Creates a punch position animation that returns to the starting position
        /// </summary>
        /// <param name="target">Target transform</param>
        /// <param name="punch">The punch strength (offset from current position)</param>
        /// <param name="duration">Animation duration</param>
        /// <param name="vibrato">Vibration count (default: 10)</param>
        /// <param name="elasticity">Elasticity of the animation (default: 1)</param>
        /// <returns>The tween</returns>
        public static Sequence UPunchPosition(this Transform target, Vector3 punch, float duration, int vibrato = 10,
            float elasticity = 1, bool snapping = false)
        {
            Vector3 startPos = target.position;

            // Create a sequence for the punch animation
            Sequence sequence = UTween.Sequence();

            // First move in the punch direction
            sequence.Append(
                UTween.To(
                        () => target.position,
                        x => target.position = x,
                        startPos + punch,
                        duration * 0.25f
                    )
                    .SetOptions(snapping)
                    .SetEase(Ease.OutQuad)
            );

            // Then bounce back with vibration
            sequence.Append(
                UTween.To(
                        () => target.position,
                        x => target.position = x,
                        startPos,
                        duration * 0.75f
                    )
                    .SetOptions(snapping)
                    .SetEase(Ease.InOutElastic)
            );

            return sequence;
        }

        public static Sequence UPunchRotation(this Transform target, Vector3 punch, float duration, int vibrato = 10,
            float elasticity = 1)
        {
            Quaternion startRot = target.rotation;

            // Create a sequence for the punch animation
            Sequence sequence = UTween.Sequence();

            // First rotate in the punch direction
            sequence.Append(
                UTween.To(
                        () => target.rotation,
                        x => target.rotation = x,
                        startRot * Quaternion.Euler(punch),
                        duration * 0.25f
                    )
                    .SetEase(Ease.OutQuad)
            );

            // Then bounce back with vibration
            sequence.Append(
                UTween.To(
                        () => target.rotation,
                        x => target.rotation = x,
                        startRot,
                        duration * 0.75f
                    )
                    .SetEase(Ease.InOutElastic)
            );

            return sequence;
        }

        public static Sequence UPunchScale(this Transform target, Vector3 punch, float duration, int vibrato = 10,
            float elasticity = 1)
        {
            Vector3 startScale = target.localScale;

            // Create a sequence for the punch animation
            Sequence sequence = UTween.Sequence();

            // First scale in the punch direction
            sequence.Append(
                UTween.To(
                        () => target.localScale,
                        x => target.localScale = x,
                        startScale + punch,
                        duration * 0.25f
                    )
                    .SetEase(Ease.OutQuad)
            );

            // Then bounce back with vibration
            sequence.Append(
                UTween.To(
                        () => target.localScale,
                        x => target.localScale = x,
                        startScale,
                        duration * 0.75f
                    )
                    .SetEase(Ease.InOutElastic)
            );

            return sequence;
        }

        /// <summary>
        /// Creates a shake position animation
        /// </summary>
        /// <param name="target">Target transform</param>
        /// <param name="duration">Animation duration</param>
        /// <param name="strength">Shake strength</param>
        /// <param name="vibrato">Vibration count (default: 10)</param>
        /// <param name="randomness">Randomness of the shake (default: 90)</param>
        /// <param name="fadeOut">Whether to fade out the shake (default: true)</param>
        /// <returns>The tween</returns>
        public static Sequence UShakePosition(this Transform target, float duration, float strength, int vibrato = 10,
            float randomness = 90, bool snapping = false, bool fadeOut = true)
        {
            Vector3 startPos = target.position;
            System.Random random = new System.Random();

            // Create sequence for shake animation
            Sequence sequence = UTween.Sequence();

            float shakeDuration = duration / vibrato;
            float strengthFactor = 1f;

            // Add vibration movements
            for (int i = 0; i < vibrato; i++)
            {
                // Apply randomness within strength
                float randomAngle = (float)(random.NextDouble() * 2 * Math.PI);
                float randomDistance = (float)(random.NextDouble() * strength * strengthFactor);

                // Create random offset
                Vector3 offset = new Vector3(
                    Mathf.Cos(randomAngle) * randomDistance,
                    Mathf.Sin(randomAngle) * randomDistance,
                    (random.Next(0, 2) == 0 ? -1 : 1) * (float)random.NextDouble() * randomDistance * 0.5f
                );

                // Add position tween
                sequence.Append(
                    UTween.To(
                        () => target.position,
                        x => target.position = x,
                        startPos + offset,
                        shakeDuration
                    ).SetEase(Ease.OutQuad)
                );

                // Reduce strength if fading out
                if (fadeOut)
                {
                    strengthFactor *= 0.9f;
                }
            }

            // Return to original position at the end
            sequence.Append(
                UTween.To(
                    () => target.position,
                    x => target.position = x,
                    startPos,
                    shakeDuration
                ).SetEase(Ease.InQuad)
            );

            return sequence;
        }

        public static Sequence UShakePosition(this Transform target, float duration, Vector3 strength, int vibrato = 10,
            float randomness = 90, bool snapping = false, bool fadeOut = true)
        {
            Vector3 startPos = target.position;
            System.Random random = new System.Random();
        
            // Create sequence for shake animation
            Sequence sequence = UTween.Sequence();
        
            float shakeDuration = duration / vibrato;
            float strengthFactor = 1f;
        
            // Add vibration movements
            for (int i = 0; i < vibrato; i++)
            {
                // Apply randomness within strength
                Vector3 randomStrength = new Vector3(
                    (random.Next(0, 2) == 0 ? -1 : 1) * (float)random.NextDouble() * strength.x * strengthFactor,
                    (random.Next(0, 2) == 0 ? -1 : 1) * (float)random.NextDouble() * strength.y * strengthFactor,
                    (random.Next(0, 2) == 0 ? -1 : 1) * (float)random.NextDouble() * strength.z * strengthFactor
                );
        
                // Add position tween
                sequence.Append(
                    (Tween<Vector3>)(UTween.To(
                        () => target.position,
                        x => target.position = x,
                        startPos + randomStrength,
                        shakeDuration
                    )
                    .SetOptions(snapping)
                    .SetEase(Ease.OutQuad))
                     
                );
            
                // Reduce strength if fading out
                if (fadeOut)
                {
                    strengthFactor *= 0.9f;
                }
            }
        
            // Return to original position at the end
            sequence.Append(
                UTween.To(
                    () => target.position,
                    x => target.position = x,
                    startPos,
                    shakeDuration
                )
                .SetOptions(snapping)
                    .SetEase(Ease.InQuad)
                 
            );
        
            return sequence;
        }

        /// <summary>
        /// Creates a shake rotation animation
        /// </summary>
        public static Sequence UShakeRotation(this Transform target, float duration, float strength, int vibrato = 10,
            float randomness = 90, bool fadeOut = true)
        {
            Quaternion startRot = target.rotation;
            System.Random random = new System.Random();

            // Create sequence for shake animation
            Sequence sequence = UTween.Sequence();

            float shakeDuration = duration / vibrato;
            float strengthFactor = 1f;

            // Add vibration movements
            for (int i = 0; i < vibrato; i++)
            {
                // Random rotation angles
                Vector3 randomEuler = new Vector3(
                    (random.Next(0, 2) == 0 ? -1 : 1) * (float)random.NextDouble() * strength * strengthFactor,
                    (random.Next(0, 2) == 0 ? -1 : 1) * (float)random.NextDouble() * strength * strengthFactor,
                    (random.Next(0, 2) == 0 ? -1 : 1) * (float)random.NextDouble() * strength * strengthFactor
                );

                Quaternion targetRot = startRot * Quaternion.Euler(randomEuler);

                // Add rotation tween
                sequence.Append(
                    UTween.To(
                        () => target.rotation,
                        x => target.rotation = x,
                        targetRot,
                        shakeDuration
                    ).SetEase(Ease.OutQuad)
                );

                // Reduce strength if fading out
                if (fadeOut)
                {
                    strengthFactor *= 0.9f;
                }
            }

            // Return to original rotation at the end
            sequence.Append(
                UTween.To(
                    () => target.rotation,
                    x => target.rotation = x,
                    startRot,
                    shakeDuration
                ).SetEase(Ease.InQuad)
            );

            return sequence;
        }

        public static Sequence UShakeRotation(this Transform target, float duration, Vector3 strength, int vibrato = 10,
            float randomness = 90, bool fadeOut = true)
        {
            Quaternion startRot = target.rotation;
            System.Random random = new System.Random();

            // Create sequence for shake animation
            Sequence sequence = UTween.Sequence();

            float shakeDuration = duration / vibrato;
            float strengthFactor = 1f;

            // Add vibration movements
            for (int i = 0; i < vibrato; i++)
            {
                // Random rotation angles
                Vector3 randomEuler = new Vector3(
                    (random.Next(0, 2) == 0 ? -1 : 1) * (float)random.NextDouble() * strength.x * strengthFactor,
                    (random.Next(0, 2) == 0 ? -1 : 1) * (float)random.NextDouble() * strength.y * strengthFactor,
                    (random.Next(0, 2) == 0 ? -1 : 1) * (float)random.NextDouble() * strength.z * strengthFactor
                );

                Quaternion targetRot = startRot * Quaternion.Euler(randomEuler);

                // Add rotation tween
                sequence.Append(
                    UTween.To(
                        () => target.rotation,
                        x => target.rotation = x,
                        targetRot,
                        shakeDuration
                    ).SetEase(Ease.OutQuad)
                );

                // Reduce strength if fading out
                if (fadeOut)
                {
                    strengthFactor *= 0.9f;
                }
            }

            // Return to original rotation at the end
            sequence.Append(
                UTween.To(
                    () => target.rotation,
                    x => target.rotation = x,
                    startRot,
                    shakeDuration
                ).SetEase(Ease.InQuad)
            );

            return sequence;
        }

        /// <summary>
        /// Creates a shake scale animation
        /// </summary>
        public static Sequence UShakeScale(this Transform target, float duration, float strength, int vibrato = 10,
            bool fadeOut = true)
        {
            Vector3 startScale = target.localScale;
            System.Random random = new System.Random();

            // Create sequence for shake animation
            Sequence sequence = UTween.Sequence();

            float shakeDuration = duration / vibrato;
            float strengthFactor = 1f;

            // Add vibration movements
            for (int i = 0; i < vibrato; i++)
            {
                // Random scale factors
                Vector3 randomScale = new Vector3(
                    startScale.x * (1 + ((random.Next(0, 2) == 0 ? -1 : 1) * (float)random.NextDouble() * strength *
                                         strengthFactor)),
                    startScale.y * (1 + ((random.Next(0, 2) == 0 ? -1 : 1) * (float)random.NextDouble() * strength *
                                         strengthFactor)),
                    startScale.z * (1 + ((random.Next(0, 2) == 0 ? -1 : 1) * (float)random.NextDouble() * strength *
                                         strengthFactor))
                );

                // Add scale tween
                sequence.Append(
                    UTween.To(
                        () => target.localScale,
                        x => target.localScale = x,
                        randomScale,
                        shakeDuration
                    ).SetEase(Ease.OutQuad)
                );

                // Reduce strength if fading out
                if (fadeOut)
                {
                    strengthFactor *= 0.9f;
                }
            }

            // Return to original scale at the end
            sequence.Append(
                UTween.To(
                    () => target.localScale,
                    x => target.localScale = x,
                    startScale,
                    shakeDuration
                ).SetEase(Ease.InQuad)
            );

            return sequence;
        }

        public static Sequence UShakeScale(this Transform target, float duration, Vector3 strength, int vibrato = 10,
            bool fadeOut = true)
        {
            Vector3 startScale = target.localScale;
            System.Random random = new System.Random();
        
            // Create sequence for shake animation
            Sequence sequence = UTween.Sequence();
        
            float shakeDuration = duration / vibrato;
            float strengthFactor = 1f;
        
            // Add vibration movements
            for (int i = 0; i < vibrato; i++)
            {
                // Random scale factors
                Vector3 randomScale = new Vector3(
                    startScale.x * (1 + ((random.Next(0, 2) == 0 ? -1 : 1) * (float)random.NextDouble() * strength.x * strengthFactor)),
                    startScale.y * (1 + ((random.Next(0, 2) == 0 ? -1 : 1) * (float)random.NextDouble() * strength.y * strengthFactor)),
                    startScale.z * (1 + ((random.Next(0, 2) == 0 ? -1 : 1) * (float)random.NextDouble() * strength.z * strengthFactor))
                );
        
                // Add scale tween
                sequence.Append(
                    UTween.To(
                        () => target.localScale,
                        x => target.localScale = x,
                        randomScale,
                        shakeDuration
                    ).SetEase(Ease.OutQuad)
                );
        
                // Reduce strength if fading out
                if (fadeOut)
                {
                    strengthFactor *= 0.9f;
                }
            }
        
            // Return to original scale at the end
            sequence.Append(
                UTween.To(
                    () => target.localScale,
                    x => target.localScale = x,
                    startScale,
                    shakeDuration
                ).SetEase(Ease.InQuad)
            );
        
            return sequence;
        }

        public static Sequence UShakeScale(this Transform target, float duration, Vector3 strength, int vibrato = 10,
            float randomness = 90,
            bool fadeOut = true)
        {
            Vector3 startScale = target.localScale;
            System.Random random = new System.Random();
        
            // Create sequence for shake animation
            Sequence sequence = UTween.Sequence();
        
            float shakeDuration = duration / vibrato;
            float strengthFactor = 1f;
        
            // Add vibration movements
            for (int i = 0; i < vibrato; i++)
            {
                // Apply randomness to strength
                float randomFactor = 1f + ((randomness - 90) / 90f); // Normalize randomness around 90
                Vector3 randomScale = new Vector3(
                    startScale.x * (1f + ((random.Next(0, 2) == 0 ? -1 : 1) * (float)random.NextDouble() * strength.x * strengthFactor * randomFactor)),
                    startScale.y * (1f + ((random.Next(0, 2) == 0 ? -1 : 1) * (float)random.NextDouble() * strength.y * strengthFactor * randomFactor)),
                    startScale.z * (1f + ((random.Next(0, 2) == 0 ? -1 : 1) * (float)random.NextDouble() * strength.z * strengthFactor * randomFactor))
                );
        
                // Add scale tween
                sequence.Append(
                    UTween.To(
                        () => target.localScale,
                        x => target.localScale = x,
                        randomScale,
                        shakeDuration
                    ).SetEase(Ease.OutQuad)
                );
            
                // Reduce strength if fading out
                if (fadeOut)
                {
                    strengthFactor *= 0.9f;
                }
            }
        
            // Return to original scale at the end
            sequence.Append(
                UTween.To(
                    () => target.localScale,
                    x => target.localScale = x,
                    startScale,
                    shakeDuration
                ).SetEase(Ease.InQuad)
            );
        
            return sequence;
        }
        

        #endregion

        #region RectTransform Extensions

        /// <summary>
        /// Tweens the RectTransform's anchored position
        /// </summary>
        public static ITween UAnchorPos(this RectTransform target, Vector2 endValue, float duration)
        {
            return UTween.To(() => target.anchoredPosition, x => target.anchoredPosition = x, endValue, duration)
                .SetTarget(target);
        }

        /// <summary>
        /// Tweens the RectTransform's anchored position on the X axis
        /// </summary>
        public static ITween UAnchorPosX(this RectTransform target, float endValue, float duration)
        {
            return UTween.To(
                () => target.anchoredPosition.x,
                x => target.anchoredPosition = new Vector2(x, target.anchoredPosition.y),
                endValue,
                duration
            ).SetTarget(target);
        }

        /// <summary>
        /// Tweens the RectTransform's anchored position on the Y axis
        /// </summary>
        public static ITween UAnchorPosY(this RectTransform target, float endValue, float duration)
        {
            return UTween.To(
                () => target.anchoredPosition.y,
                y => target.anchoredPosition = new Vector2(target.anchoredPosition.x, y),
                endValue,
                duration
            ).SetTarget(target);
        }

        /// <summary>
        /// Tweens the RectTransform's size delta
        /// </summary>
        public static ITween USizeDelta(this RectTransform target, Vector2 endValue, float duration)
        {
            return UTween.To(() => target.sizeDelta, x => target.sizeDelta = x, endValue, duration)
                .SetTarget(target);
        }

        /// <summary>
        /// Tweens the RectTransform's pivot
        /// </summary>
        public static ITween UPivot(this RectTransform target, Vector2 endValue, float duration)
        {
            return UTween.To(() => target.pivot, x => target.pivot = x, endValue, duration)
                .SetTarget(target);
        }

        #endregion

        #region CanvasGroup Extensions

        /// <summary>
        /// Tweens the CanvasGroup's alpha value
        /// </summary>
        public static ITween UFade(this CanvasGroup target, float endValue, float duration)
        {
            return UTween.To(() => target.alpha, x => target.alpha = x, endValue, duration)
                .SetTarget(target);
        }

        #endregion

        #region Graphic Extensions

        /// <summary>
        /// Tweens the Graphic's color
        /// </summary>
        public static ITween UColor(this Graphic target, Color endValue, float duration)
        {
            return UTween.To(() => target.color, x => target.color = x, endValue, duration)
                .SetTarget(target);
        }

        /// <summary>
        /// Tweens the Graphic's alpha value
        /// </summary>
        public static ITween UFade(this Graphic target, float endValue, float duration)
        {
            return UTween.To(
                () => target.color.a,
                a =>
                {
                    Color c = target.color;
                    c.a = a;
                    target.color = c;
                },
                endValue,
                duration
            ).SetTarget(target);
        }

        #endregion

        #region Image Extensions

        /// <summary>
        /// Tweens the Image's fill amount
        /// </summary>
        public static ITween UFillAmount(this Image target, float endValue, float duration)
        {
            return UTween.To(() => target.fillAmount, x => target.fillAmount = x, endValue, duration)
                .SetTarget(target);
        }

        #endregion

        #region Text Extensions

        /// <summary>
        /// Tweens the Text component's content character by character
        /// </summary>
        public static ITween UText(this Text target, string endValue, float duration)
        {
            string startValue = target.text;

            return UTweenVirtual.Float(0, 1, duration, t =>
            {
                // Calculate how many characters to show
                int length = Mathf.RoundToInt(Mathf.Lerp(0, endValue.Length, t));

                // Set the text to a substring of the target text
                target.text = endValue.Substring(0, length);
            }).SetTarget(target);
        }
        
        public static ITween UText(this Text target, string endValue, float duration, bool richText,
            ScrambleMode scrambleMode = ScrambleMode.None)
        {
            string startValue = target.text;

            return UTweenVirtual.String(startValue, endValue, duration, t =>
            {
                // Set the text to the current value
                target.text = t;
            }, richText, scrambleMode).SetTarget(target);
        }

        /// <summary>
        /// Tweens the Text component's font size
        /// </summary>
        public static ITween UFontSize(this Text target, int endValue, float duration)
        {
            return UTween.To(() => target.fontSize, x => target.fontSize = x, endValue, duration)
                .SetTarget(target);
        }
        
        public static ITween UCounter(this Text target, int startValue, int endValue, float duration, bool thousandsSeparator = true)
        {
            return UTweenVirtual.Float(startValue, endValue, duration, t =>
            {
                // Calculate the current value
                int currentValue = Mathf.RoundToInt(t);

                // Format the value with thousands separator if needed
                string formattedValue = thousandsSeparator ? string.Format("{0:n0}", currentValue) : currentValue.ToString();

                // Set the text to the formatted value
                target.text = formattedValue;
            }).SetTarget(target);
        }

        #endregion

        #region Renderer Extensions

        /// <summary>
        /// Tweens the Renderer's material color
        /// </summary>
        public static ITween UColor(this Renderer target, Color endValue, float duration)
        {
            return UTween.To(
                () => target.material.color,
                x => target.material.color = x,
                endValue,
                duration
            ).SetTarget(target);
        }

        /// <summary>
        /// Tweens a specific color property of the Renderer's material
        /// </summary>
        public static ITween UMaterialColor(this Renderer target, string propertyName, Color endValue, float duration)
        {
            return UTween.To(
                () => target.material.GetColor(propertyName),
                x => target.material.SetColor(propertyName, x),
                endValue,
                duration
            ).SetTarget(target);
        }

        /// <summary>
        /// Tweens a specific float property of the Renderer's material
        /// </summary>
        public static ITween UMaterialFloat(this Renderer target, string propertyName, float endValue, float duration)
        {
            return UTween.To(
                () => target.material.GetFloat(propertyName),
                x => target.material.SetFloat(propertyName, x),
                endValue,
                duration
            ).SetTarget(target);
        }

        #endregion

        #region Light Extensions

        /// <summary>
        /// Tweens the Light's intensity
        /// </summary>
        public static ITween UIntensity(this Light target, float endValue, float duration)
        {
            return UTween.To(() => target.intensity, x => target.intensity = x, endValue, duration)
                .SetTarget(target);
        }

        /// <summary>
        /// Tweens the Light's range
        /// </summary>
        public static ITween URange(this Light target, float endValue, float duration)
        {
            return UTween.To(() => target.range, x => target.range = x, endValue, duration)
                .SetTarget(target);
        }

        /// <summary>
        /// Tweens the Light's color
        /// </summary>
        public static ITween UColor(this Light target, Color endValue, float duration)
        {
            return UTween.To(() => target.color, x => target.color = x, endValue, duration)
                .SetTarget(target);
        }

        #endregion

        #region Camera Extensions

        /// <summary>
        /// Tweens the Camera's field of view
        /// </summary>
        public static ITween UFOV(this Camera target, float endValue, float duration)
        {
            return UTween.To(() => target.fieldOfView, x => target.fieldOfView = x, endValue, duration)
                .SetTarget(target);
        }

        /// <summary>
        /// Tweens the Camera's orthographic size
        /// </summary>
        public static ITween UOrthoSize(this Camera target, float endValue, float duration)
        {
            return UTween.To(() => target.orthographicSize, x => target.orthographicSize = x, endValue, duration)
                .SetTarget(target);
        }

        /// <summary>
        /// Tweens the Camera's background color
        /// </summary>
        public static ITween UBackgroundColor(this Camera target, Color endValue, float duration)
        {
            return UTween.To(() => target.backgroundColor, x => target.backgroundColor = x, endValue, duration)
                .SetTarget(target);
        }

        /// <summary>
        /// Tweens the Camera's color
        /// </summary>
        public static ITween UColor(this Camera target, Color endValue, float duration)
        {
            return UTween.To(() => target.backgroundColor, x => target.backgroundColor = x, endValue, duration)
                .SetTarget(target);
        }

        /// <summary>
        /// Twens Field of View
        /// zummary>
        public static ITween UFieldOfView(this Camera target, float endValue, float duration)
        {
            return UTween.To(() => target.fieldOfView, x => target.fieldOfView = x, endValue, duration)
                .SetTarget(target);
        }

        #endregion

        #region Textmesh Pro Extensions

        /// <summary>
        /// Tweens the TextMeshProUGUI component's content character by character
        /// </summary>
        public static ITween UText(this TMPro.TextMeshProUGUI target, string endValue, float duration)
        {
            string startValue = target.text;

            return UTweenVirtual.Float(0, 1, duration, t =>
            {
                // Calculate how many characters to show
                int length = Mathf.RoundToInt(Mathf.Lerp(0, endValue.Length, t));

                // Set the text to a substring of the target text
                target.text = endValue.Substring(0, length);
            }).SetTarget(target);
        }
        
        public static ITween UText(this TMP_Text target, string endValue, float duration)
        {
            string startValue = target.text;

            return UTweenVirtual.Float(0, 1, duration, t =>
            {
                // Calculate how many characters to show
                int length = Mathf.RoundToInt(Mathf.Lerp(0, endValue.Length, t));

                // Set the text to a substring of the target text
                target.text = endValue.Substring(0, length);
            }).SetTarget(target);
        }
        
        public static ITween UText(this TMPro.TextMeshProUGUI target, string endValue, float duration, bool richText,
            ScrambleMode scrambleMode = ScrambleMode.None)
        {
            string startValue = target.text;

            return UTweenVirtual.String(startValue, endValue, duration, t =>
            {
                // Set the text to the current value
                target.text = t;
            }, richText, scrambleMode).SetTarget(target);
        }
        
        public static ITween UText(this TMP_Text target, string endValue, float duration, bool richText,
            ScrambleMode scrambleMode = ScrambleMode.None)
        {
            string startValue = target.text;

            return UTweenVirtual.String(startValue, endValue, duration, t =>
            {
                // Set the text to the current value
                target.text = t;
            }, richText, scrambleMode).SetTarget(target);
        }

        /// <summary>
        /// Tweens the Color of the TextMeshProUGUI component
        /// </summary>
        public static ITween UColor(this TMPro.TextMeshProUGUI target, Color endValue, float duration)
        {
            return UTween.To(() => target.color, x => target.color = x, endValue, duration)
                .SetTarget(target);
        }

        #endregion

        #region Material Extensions

        public static ITween UColor(this Material target, Color endValue, float duration)
        {
            return UTween.To(() => target.color, x => target.color = x, endValue, duration)
                .SetTarget(target);
        }

        public static ITween UOffset(this Material target, Vector2 endValue, float duration)
        {
            return UTween.To(() => target.mainTextureOffset, x => target.mainTextureOffset = x, endValue, duration)
                .SetTarget(target);
        }

        public static ITween UTiling(this Material target, Vector2 endValue, float duration)
        {
            return UTween.To(() => target.mainTextureScale, x => target.mainTextureScale = x, endValue, duration)
                .SetTarget(target);
        }

        #endregion

        #region AudioSource Extensions

        /// <summary>
        /// Tweens the AudioSource's volume
        /// </summary>
        public static ITween UVolume(this AudioSource target, float endValue, float duration)
        {
            return UTween.To(() => target.volume, x => target.volume = x, endValue, duration)
                .SetTarget(target);
        }

        /// <summary>
        /// Tweens the AudioSource's pitch
        /// </summary>
        public static ITween UPitch(this AudioSource target, float endValue, float duration)
        {
            return UTween.To(() => target.pitch, x => target.pitch = x, endValue, duration)
                .SetTarget(target);
        }

        /// <summary>
        /// Tweens the AudioSource's spatial blend
        /// </summary>
        public static ITween USpatialBlend(this AudioSource target, float endValue, float duration)
        {
            return UTween.To(() => target.spatialBlend, x => target.spatialBlend = x, endValue, duration)
                .SetTarget(target);
        }

        /// <summary>
        /// Tweenns fade in the AudioSource
        /// </summary>
        public static ITween UFade(this AudioSource target, float endValue, float duration)
        {
            return UTween.To(() => target.volume, x => target.volume = x, endValue, duration)
                .SetTarget(target);
        }

        #endregion

        #region Slider
        public static ITween UValue(this Slider target, float endValue, float duration, bool snapping = false)
        {
            return UTween.To(() => target.value, x => target.value = x, endValue, duration)
                .SetOptions(snapping)
                .SetTarget(target);
        }
        #endregion
    }
}