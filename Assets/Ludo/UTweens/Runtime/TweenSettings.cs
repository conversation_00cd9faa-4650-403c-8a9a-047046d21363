namespace Ludo.UTweens
{
    /// <summary>
    /// Global settings for UTween
    /// </summary>
    public class TweenSettings
    {
        public Ease DefaultEase { get; set; } = Ease.OutQuad;
        public bool DefaultAutoKill { get; set; } = true;
        public bool DefaultRecyclable { get; set; } = true;
        public UpdateType DefaultUpdateType { get; set; } = UpdateType.Normal;
        public bool DefaultTimeScaleIndependent { get; set; } = false;
        public bool SafeMode { get; set; } = true;
        public LogBehavior LogBehavior { get; set; } = LogBehavior.Default;
    }
}