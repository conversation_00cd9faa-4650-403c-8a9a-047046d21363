using System;
using System.Collections.Generic;
using Ludo.UTweens.Plugins;
using UnityEngine;

namespace Ludo.UTweens
{
    /// <summary>
    /// Main entry point for the UTween library. Provides static methods to create and control tweens.
    /// </summary>
    public static class UTween
    {
        #region Settings and Initialization

        private static bool _initialized;
        private static TweenSettings _defaultSettings = new TweenSettings();
        private static TweenRunner _runner;
        private static int _initialCapacity = 200;
        private static readonly ObjectPool<TweenData> _tweenPool = new ObjectPool<TweenData>(() => new TweenData());
        private static List<ITween> _activeTweens;

        private static Dictionary<object, List<ITween>> _targetToTweens;

        private static Dictionary<object, List<ITween>> _idToTweens;

        /// <summary>
        /// Default settings for all tweens
        /// </summary>
        public static TweenSettings DefaultSettings => _defaultSettings;

        /// <summary>
        /// Total number of active tweens
        /// </summary>
        public static int TotalActiveTweens => _activeTweens.Count;
        
        public static bool Initialized => _initialized;

        /// <summary>
        /// Initialize UTween with optional settings
        /// </summary>
        // In UTween.cs, modify the Init() method:
        public static void Init(bool? safeMode = null, int? tweensCapacity = null, LogBehavior? logBehavior = null)
        {
            if (_initialized) return;

            _initialized = true;

            if (tweensCapacity.HasValue)
                _initialCapacity = tweensCapacity.Value;

            if (safeMode.HasValue)
                _defaultSettings.SafeMode = safeMode.Value;

            if (logBehavior.HasValue)
                _defaultSettings.LogBehavior = logBehavior.Value;
    
            _activeTweens = new List<ITween>(_initialCapacity);
            _targetToTweens = new Dictionary<object, List<ITween>>(_initialCapacity / 4);
            _idToTweens = new Dictionary<object, List<ITween>>(_initialCapacity / 8);

            // Create the runner - add check for editor mode
            GameObject runnerGO = new GameObject("[UTween]");
            // Only use DontDestroyOnLoad in play mode
            if (Application.isPlaying)
            {
                GameObject.DontDestroyOnLoad(runnerGO);
            }
            _runner = runnerGO.AddComponent<TweenRunner>();

            // Debug.Log($"UTween initialized. Capacity: {_initialCapacity}, Safe mode: {_defaultSettings.SafeMode}");
        }

        /// <summary>
        /// Set the initial capacity for tweens to optimize memory usage
        /// </summary>
        public static void SetTweensCapacity(int tweensCapacity)
        {
            _initialCapacity = tweensCapacity;
        }

        /// <summary>
        /// Configure global default settings
        /// </summary>
        public static void SetGlobalDefaults(Ease? defaultEase = null, bool? defaultAutoKill = null,
            bool? defaultRecyclable = null, UpdateType? defaultUpdateType = null,
            bool? defaultTimeScaleIndependent = null, bool? safeMode = null)
        {
            if (defaultEase.HasValue)
                _defaultSettings.DefaultEase = defaultEase.Value;

            if (defaultAutoKill.HasValue)
                _defaultSettings.DefaultAutoKill = defaultAutoKill.Value;

            if (defaultRecyclable.HasValue)
                _defaultSettings.DefaultRecyclable = defaultRecyclable.Value;

            if (defaultUpdateType.HasValue)
                _defaultSettings.DefaultUpdateType = defaultUpdateType.Value;

            if (defaultTimeScaleIndependent.HasValue)
                _defaultSettings.DefaultTimeScaleIndependent = defaultTimeScaleIndependent.Value;

            if (safeMode.HasValue)
                _defaultSettings.SafeMode = safeMode.Value;
        }
        
        public static void Cleanup()
        {
            if (_runner != null)
            {
                // Destroy the runner GameObject
                UnityEngine.Object.DestroyImmediate(_runner.gameObject);
                _runner = null;
            }
    
            // Reset the initialization state
            _initialized = false;
    
            // Clear collections
            if (_activeTweens != null) _activeTweens.Clear();
            if (_targetToTweens != null) _targetToTweens.Clear();
            if (_idToTweens != null) _idToTweens.Clear();
        }

        #endregion

        #region Tween Creation

        /// <summary>
        /// Creates a new sequence for combining multiple tweens
        /// </summary>
        public static Sequence Sequence()
        {
            EnsureInitialized();
            return new Sequence();
        }

        /// <summary>
        /// Generic method to create a tween for any supported type
        /// </summary>
        public static Tween<T> To<T>(Func<T> getter, Action<T> setter, T endValue, float duration)
        {
            EnsureInitialized();

            // Check if we have a plugin for this type
            if (!PluginManager.HasPlugin<T>())
            {
                throw new ArgumentException($"No plugin available for type {typeof(T)}");
            }

            TweenData data = _tweenPool.Get();
            data.Init(duration);

            Tween<T> tween = new Tween<T>(data, getter, setter, endValue);
            RegisterTween(tween);

            return tween;
        }
        
        

        #endregion

        #region Tween Management

        /// <summary>
        /// Pauses all tweens
        /// </summary>
        public static void PauseAll()
        {
            foreach (ITween tween in _activeTweens)
            {
                tween.Pause();
            }
        }

        /// <summary>
        /// Plays all paused tweens
        /// </summary>
        public static void PlayAll()
        {
            foreach (ITween tween in _activeTweens)
            {
                tween.Play();
            }
        }

        /// <summary>
        /// Restarts all tweens
        /// </summary>
        public static void RestartAll(bool includeDelay = true)
        {
            foreach (ITween tween in _activeTweens)
            {
                tween.Restart(includeDelay);
            }
        }

        /// <summary>
        /// Rewinds all tweens
        /// </summary>
        public static void RewindAll(bool includeDelay = true)
        {
            foreach (ITween tween in _activeTweens)
            {
                tween.Rewind(includeDelay);
            }
        }

        /// <summary>
        /// Completes all tweens
        /// </summary>
        public static void CompleteAll(bool withCallbacks = false)
        {
            foreach (ITween tween in _activeTweens)
            {
                tween.Complete(withCallbacks);
            }
        }

        /// <summary>
        /// Kills all tweens
        /// </summary>
        public static void KillAll(bool complete = false)
        {
            for (int i = _activeTweens.Count - 1; i >= 0; i--)
            {
                _activeTweens[i].Kill(complete);
            }
        }

        /// <summary>
        /// Pauses all tweens with the specified target
        /// </summary>
        public static void Pause(object target)
        {
            if (!_targetToTweens.TryGetValue(target, out List<ITween> tweens)) return;

            foreach (ITween tween in tweens)
            {
                tween.Pause();
            }
        }

        /// <summary>
        /// Plays all tweens with the specified target
        /// </summary>
        public static void Play(object target)
        {
            if (!_targetToTweens.TryGetValue(target, out List<ITween> tweens)) return;

            foreach (ITween tween in tweens)
            {
                tween.Play();
            }
        }

        /// <summary>
        /// Kills all tweens with the specified target
        /// </summary>
        public static void Kill(object target, bool complete = false)
        {
            if (target == null || _targetToTweens == null) return;
            if (!_targetToTweens.TryGetValue(target, out List<ITween> tweens)) return;

            for (int i = tweens.Count - 1; i >= 0; i--)
            {
                tweens[i].Kill(complete);
            }
        }

        /// <summary>
        /// Kills all tweens with the specified ID
        /// </summary>
        public static void Kill(object target, object id, bool complete = false)
        {
            if (!_idToTweens.TryGetValue(id, out List<ITween> tweens)) return;

            for (int i = tweens.Count - 1; i >= 0; i--)
            {
                ITween tween = tweens[i];
                if (tween.Target == target)
                {
                    tween.Kill(complete);
                }
            }
        }

        /// <summary>
        /// Returns true if there are any tweens animating the specified target
        /// </summary>
        public static bool IsTweening(object target)
        {
            return _targetToTweens.TryGetValue(target, out List<ITween> tweens) && tweens.Count > 0;
        }

        #endregion

        #region Internal Methods

        internal static void OnTweenUpdateTypeChanged(ITween tween, UpdateType oldType, UpdateType newType)
        {
            if (_runner == null) return;

            // Unregister from old update type
            _runner.UnregisterTween(tween, oldType);

            // Register with new update type
            _runner.RegisterTween(tween, newType);
        }

        internal static void RegisterTween(ITween tween)
        {
            EnsureInitialized();

            _activeTweens.Add(tween);

            // Register with TweenRunner based on update type
            _runner.RegisterTween(tween, tween.UpdateType);

            // Register by target
            if (tween.Target != null)
            {
                if (!_targetToTweens.TryGetValue(tween.Target, out List<ITween> targetTweens))
                {
                    targetTweens = new List<ITween>(8);
                    _targetToTweens[tween.Target] = targetTweens;
                }

                targetTweens.Add(tween);
            }

            // Register by ID if one is set later
            tween.OnIdChangedEvent += OnTweenIdChanged;
        }

        internal static void UnregisterTween(ITween tween)
        {
            _activeTweens.Remove(tween);

            // Unregister from TweenRunner
            _runner.UnregisterTween(tween, tween.UpdateType);

            // Unregister from target
            if (tween.Target != null && _targetToTweens.TryGetValue(tween.Target, out List<ITween> targetTweens))
            {
                targetTweens.Remove(tween);
                if (targetTweens.Count == 0)
                {
                    _targetToTweens.Remove(tween.Target);
                }
            }

            // Unregister from ID
            if (tween.Id != null && _idToTweens.TryGetValue(tween.Id, out List<ITween> idTweens))
            {
                idTweens.Remove(tween);
                if (idTweens.Count == 0)
                {
                    _idToTweens.Remove(tween.Id);
                }
            }

            tween.OnIdChangedEvent -= OnTweenIdChanged;

            // Return to pool if recyclable
            if (tween.Recyclable && tween is Tween rawTween)
            {
                _tweenPool.Release(rawTween.TweenData);
            }
        }

        private static void OnTweenIdChanged(ITween tween, object oldId, object newId)
        {
            // Remove from old ID
            if (oldId != null && _idToTweens.TryGetValue(oldId, out List<ITween> oldIdTweens))
            {
                oldIdTweens.Remove(tween);
                if (oldIdTweens.Count == 0)
                {
                    _idToTweens.Remove(oldId);
                }
            }

            // Add to new ID
            if (newId != null)
            {
                if (!_idToTweens.TryGetValue(newId, out List<ITween> newIdTweens))
                {
                    newIdTweens = new List<ITween>();
                    _idToTweens[newId] = newIdTweens;
                }

                newIdTweens.Add(tween);
            }
        }

        public static void ManualUpdate(float deltaTime, float unscaledDeltaTime)
        {
            if (_runner != null)
            {
                _runner.ManualUpdate(deltaTime, unscaledDeltaTime);
            }
        }

        private static void EnsureInitialized()
        {
            if (!_initialized) Init();
        }

        #endregion
    }
}